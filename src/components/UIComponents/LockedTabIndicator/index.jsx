import React from 'react';
import { Lock } from 'lucide-react';

const LockedTabIndicator = ({ className = '' }) => {
  return (
    <div className={`inline-flex items-center justify-center ml-1 ${className}`}>
      <div className="relative">
        <Lock className="w-3 h-3 text-orange-500" />
        <div className="absolute -top-1 -right-1 w-1.5 h-1.5 bg-orange-500 rounded-full animate-pulse"></div>
      </div>
    </div>
  );
};

export default LockedTabIndicator;
