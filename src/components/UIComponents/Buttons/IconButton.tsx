import React from 'react';
import Badge from "@mui/material/Badge";
import { BootstrapTooltip } from '../ToolTip/Tooltip-material-ui';

import '@/styles/components/UI.css';

interface IconButtonProps {
  icon: React.ReactNode;
  text?: string; // New prop for text
  tooltip?: string;
  onClick: (e?: React.MouseEvent) => void;
  className?: string;
  badgeContent?: number;
  placement?: 'top' | 'bottom' | 'left' | 'right';
  variant?: string;
}

export const IconButton: React.FC<IconButtonProps> = ({
  icon,
  text,
  tooltip,
  onClick,
  className = '',
  badgeContent,
  placement = 'bottom-end',
  variant = 'default'
}) => {
  const buttonContent = (
    <>
      {badgeContent !== undefined ? (
        <Badge
          badgeContent={badgeContent}
          color="primary"
          anchorOrigin={{
            vertical: "top",
            horizontal: "right",
          }}
        >
          {icon}
        </Badge>
      ) : (
        icon
      )}
      {text && <span className="ml-2 text-sm text-gray-600 font-medium">{text}</span>}
    </>
  );

  return (
    <BootstrapTooltip title={tooltip || ''} placement={placement}>
      <button
        onClick={onClick}
        className={`${className} ${variant === 'small' ? 'p-2' : 'px-3 py-2'} icon-button group flex items-center`}
        aria-label={tooltip}
      >
        {buttonContent}
      </button>
    </BootstrapTooltip>
  );
};