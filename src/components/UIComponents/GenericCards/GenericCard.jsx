const GenericCard = ({ title, description, id, type, onClick, disabled, extraInfo }) => {
  return (
    <div
      className={`bg-white rounded-lg border border-gray-200 p-4 hover:shadow-md transition-all 
        ${disabled ? 'cursor-not-allowed opacity-60' : 'cursor-pointer'}`}
      onClick={disabled ? undefined : onClick}
    >
      <div className="space-y-2">
        <div>
          <h3 className="text-lg font-semibold text-gray-900">{title}</h3>
          <p className="text-sm text-gray-600">{description}</p>
        </div>
        
        {extraInfo && (
          <div className="mt-2">
            {extraInfo}
          </div>
        )}

        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">{type}</span>
          <span className="text-sm text-purple-600">{id}</span>
        </div>
      </div>
    </div>
  );
};

export default GenericCard; 