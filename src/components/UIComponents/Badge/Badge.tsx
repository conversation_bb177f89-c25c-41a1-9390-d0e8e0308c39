import React from 'react';

interface BadgeProps {
  type?: string | null;  // Made type optional and nullable
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({ type, className }) => {
  if (!type) return null;  // Early return if type is undefined or null

  // Define badge styles based on type
  const getBadgeStyle = () => {
    switch(type.toLowerCase()) {
      case 'auto extraction':
        return 'text-purple-700 bg-purple-50 border-purple-200';
      case 're-configure':
        return 'text-orange-700 bg-orange-50 border-orange-200';
      case 'auto configure':
      default:
        return 'text-orange-700 bg-orange-50 border-orange-200';
    }
  };

  return (
    <span
      className={`inline-flex items-center px-3 py-1 text-xs font-medium rounded-md ${getBadgeStyle()} ${className || ''}`}
    >
      {type}
    </span>
  );
};

export default Badge;