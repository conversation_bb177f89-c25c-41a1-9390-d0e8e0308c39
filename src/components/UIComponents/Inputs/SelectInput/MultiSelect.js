import React, { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";

const MultiSelectComponent = ({
  options,
  selectedOptions,
  setSelectedOptions,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const containerRef = useRef(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target) &&
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isOpen && containerRef.current && dropdownRef.current) {
      const rect = containerRef.current.getBoundingClientRect();
      dropdownRef.current.style.top = `${rect.bottom + window.scrollY}px`;
      dropdownRef.current.style.left = `${rect.left + window.scrollX}px`;
      dropdownRef.current.style.width = `${rect.width}px`;
    }
  }, [isOpen]);

  const toggleOption = (option) => {
    setSelectedOptions((prevSelected) =>
      prevSelected.some((item) => item.value === option.value)
        ? prevSelected.filter((item) => item.value !== option.value)
        : [...prevSelected, option]
    );
  };

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const renderDropdown = () => (
    <div
      ref={dropdownRef}
      className="fixed z-[100000] bg-white border border-gray-300 rounded-md shadow-lg overflow-y-auto overflow-x-hidden custom-scrollbar"
      style={{ maxHeight: "200px" }}
    >
      <div className="p-3">
        <input
          type="text"
          placeholder="Search..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="w-full p-2 border border-gray-300 rounded-md text-sm"
        />
      </div>
      {filteredOptions.map((option) => (
        <label
          key={option.value}
          className="flex items-center p-1 hover:bg-gray-100 text-sm mx-3"
        >
          <input
            type="checkbox"
            checked={selectedOptions.some(
              (item) => item.value === option.value
            )}
            onChange={() => toggleOption(option)}
            className="mr-2"
          />
          <span>{option.label}</span>
        </label>
      ))}
    </div>
  );

  const getDisplayValue = () => {
    if (selectedOptions.length === 0) return "Select responsibilities...";
    if (selectedOptions.length === 1) return selectedOptions[0].label;
    return `${selectedOptions.length} responsibilities selected`;
  };

  return (
    <div ref={containerRef} className="relative w-full">
      <div
        className="w-full p-2 bg-white border border-gray-300 rounded-md cursor-pointer text-sm flex justify-between items-center"
        onClick={() => setIsOpen(!isOpen)}
      >
        <span className="truncate">{getDisplayValue()}</span>
        {isOpen ? (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="w-3.5 h-4 transition-transform transform rotate-180 mr-1.5 font-semibold text-xl"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m4.5 15.75 7.5-7.5 7.5 7.5"
            />
          </svg>
        ) : (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            strokeWidth="1.5"
            stroke="currentColor"
            className="w-3.5 h-4 transition-transform mr-1.5 font-semibold text-xl"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              d="m19.5 8.25-7.5 7.5-7.5-7.5"
            />
          </svg>
        )}
      </div>
      {isOpen && createPortal(renderDropdown(), document.body)}
    </div>
  );
};

export default MultiSelectComponent;
