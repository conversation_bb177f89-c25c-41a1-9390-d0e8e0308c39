import React from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationProps {
  currentPage: number;
  pageCount: number;
  pageSize: number;
  totalItems: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (pageSize: number) => void;
  pageSizeOptions?: number[];
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  pageCount,
  pageSize,
  totalItems,
  onPageChange,
  onPageSizeChange,
  pageSizeOptions = [5, 10, 20, 50],
}) => {
  {/*React.useEffect(() => {
    const savedPageSize = localStorage.getItem('preferredPageSize');
    if (Number(savedPageSize) != pageSize) {
      onPageSizeChange(Number(savedPageSize));
    }
  }, []);*/}

  const handlePageSizeChange = (newSize: number) => {
    localStorage.setItem('preferredPageSize', newSize.toString());
    onPageSizeChange(newSize);
  };

  const renderPageButtons = () => {
    const pages = [];

    const createButton = (pageNum: number) => {
      return (
        <button
          key={pageNum}
          onClick={() => onPageChange(pageNum)}
          className={`h-8 w-8 flex items-center justify-center border-y border-r border-gray-300 first:border-l ${currentPage === pageNum
            ? "bg-blue-50 text-blue-600"
            : "bg-white text-gray-700 hover:bg-gray-50"
            }`}
        >
          {pageNum}
        </button>
      );
    };

    const createEllipsis = (key: number | string) => {
      return (
        <span
          key={key}
          className="h-8 w-8 flex items-center justify-center border-y border-r border-gray-300 bg-white"
        >
          ...
        </span>
      );
    };

    if (pageCount <= 3) {
      for (let i = 1; i <= pageCount; i++) {
        pages.push(createButton(i));
      }
      return pages;
    }

    let windowStart = currentPage;
    let windowEnd = Math.min(currentPage + 3, pageCount);

    if (currentPage <= 3) {
      windowStart = 1;
      windowEnd = 4;
    }
    else if (currentPage > pageCount - 4) {
      windowStart = pageCount - 3;
      windowEnd = pageCount;
    }

    if (windowStart > 1) {
      pages.push(createButton(1));
      pages.push(createEllipsis('left'));
    }

    for (let i = windowStart; i <= windowEnd; i++) {
      pages.push(createButton(i));
    }

    if (windowEnd < pageCount) {
      pages.push(createEllipsis('right'));
      pages.push(createButton(pageCount));
    }

    return pages;
  };


  // Handle the case when there are no items
  const hasItems = totalItems > 0;
  const startItem = hasItems ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = hasItems ? Math.min(totalItems, currentPage * pageSize) : 0;

  return (
    <div className="bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6">
      <div className="flex items-center space-x-2">
        <span className="text-sm text-[#2A3439]">
          {hasItems
            ? `Showing ${startItem}-${endItem} of ${totalItems}`
            : `Showing 0 of 0`
          }
        </span>
      </div>

      <div className="flex items-center">
        {hasItems ? (
          <>
            <button
              onClick={() => onPageChange(Math.max(1, currentPage - 1))}
              disabled={currentPage === 1}
              className="inline-flex items-center p-1.5 rounded-l border border-gray-300 bg-white text-[#2A3439] hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            {renderPageButtons()}
            <button
              onClick={() => onPageChange(Math.min(pageCount, currentPage + 1))}
              disabled={currentPage === pageCount}
              className="inline-flex items-center p-1.5 rounded-r border border-gray-300 bg-white text-[#2A3439] hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </>
        ) : (
          <div className="flex border border-gray-300 rounded overflow-hidden">
            <button
              disabled={true}
              className="inline-flex items-center p-1.5 bg-gray-100 text-gray-400 cursor-not-allowed"
            >
              <ChevronLeft className="h-5 w-5" />
            </button>
            <div className="h-8 w-8 flex items-center justify-center border-x border-gray-300 bg-gray-100 text-gray-400">
              1
            </div>
            <button
              disabled={true}
              className="inline-flex items-center p-1.5 bg-gray-100 text-gray-400 cursor-not-allowed"
            >
              <ChevronRight className="h-5 w-5" />
            </button>
          </div>
        )}
      </div>

      <div className="flex items-center space-x-2">
        <span className="text-sm text-[#2A3439]">Rows per page</span>
        <select
          value={pageSize}
          onChange={(e) => handlePageSizeChange(Number(e.target.value))}
          className="form-select rounded-md border-gray-300 text-sm focus:border-blue-500 focus:ring-blue-500"
        >
          {pageSizeOptions.map((size) => (
            <option key={size} value={size}>
              {size}
            </option>
          ))}
        </select>
      </div>
    </div>
  );
};

export default Pagination;
