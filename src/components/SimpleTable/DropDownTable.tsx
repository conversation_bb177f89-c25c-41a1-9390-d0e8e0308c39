import { Menu } from '@headlessui/react';
import { MoreVertical } from 'lucide-react';
import { FaCaretDown } from 'react-icons/fa';
import React, { useState, useMemo, useEffect, useRef } from 'react';

interface StatusBadgeProps {
    status: 'SUCCESS' | 'FAILED' | 'RUNNING' | 'PASSED' | 'CLOSED' | 'IN PROGRESS' | 'DONE' | 'IN REVIEW';
}

interface HeaderProps {
    key: string;
    label: string;
    actionLabel?: string;
    icon?: React.ReactNode;
}

interface TableComponentProps {
    title: string;
    data: Array<{ [key: string]: any }>;
    onRowClick: (id: string, item?: any) => void;
    headers: HeaderProps[];
    togglebuttonsList?: string[];
    sortableColumns?: { [key: string]: boolean };
    itemsPerPage?: number;
    isCheckBox?: boolean;
    onActionClick?: (id: string) => void;
    onSelectionChange?: (selectedItems: Array<{ [key: string]: any }>) => void;
    onStatusChange?: (itemId: string, newStatus: string) => void;
    statusOptions?: StatusBadgeProps['status'][];
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status }) => {
    const statusConfig = {
        SUCCESS: { bgClass: 'bg-green-100', dotClass: 'bg-green-600', textClass: 'text-green-800' },
        "DONE": { bgClass: 'bg-green-100', dotClass: 'bg-green-600', textClass: 'text-green-800' },
        PASSED: { bgClass: 'bg-green-100', dotClass: 'bg-green-600', textClass: 'text-green-800' },
        RUNNING: { bgClass: 'bg-orange-100', dotClass: 'bg-orange-500', textClass: 'text-orange-800' },
        "IN PROGRESS": { bgClass: 'bg-orange-100', dotClass: 'bg-orange-500', textClass: 'text-orange-800' },
        "IN REVIEW": { bgClass: 'bg-orange-100', dotClass: 'bg-orange-500', textClass: 'text-orange-800' },
        CLOSED: { bgClass: 'bg-red-100', dotClass: 'bg-red-600', textClass: 'text-red-800' },
        FAILED: { bgClass: 'bg-red-100', dotClass: 'bg-red-600', textClass: 'text-red-800' },
        UNTESTED: { bgClass: 'bg-gray-200', dotClass: 'bg-gray-800', textClass: 'text-gray-800' },
    };

    const config = statusConfig[status] || statusConfig.FAILED;

    return (
        <div className={`inline-flex items-center justify-center rounded-md px-2 py-1 ${config.bgClass}`}>
            <div className={`w-2 h-2 rounded-full mr-1.5 flex-shrink-0 ${config.dotClass}`}></div>
            <span className={`text-xs font-semibold uppercase ${config.textClass}`}>
                {status}
            </span>
        </div>
    );
};

const StatusDropdown = ({
    status,
    currentStatus,
    onChange,
}: Readonly<{
    status?: StatusBadgeProps['status'][];
    currentStatus: StatusBadgeProps['status'];
    onChange: (newStatus: StatusBadgeProps['status']) => void;
}>) => {
    const [isOpen, setIsOpen] = useState(false);
    const dropdownRef = useRef<HTMLDivElement>(null);

    useEffect(() => {
        const handleClickOutside = (event: MouseEvent) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
                setIsOpen(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const handleStatusChange = (newStatus: any, event: React.MouseEvent) => {
        event.stopPropagation();
        onChange(newStatus);
        setIsOpen(false);
    };

    return (
        <div className="relative inline-block w-48" ref={dropdownRef}>
            <button
                onClick={(e) => {
                    e.stopPropagation();
                    setIsOpen(!isOpen);
                }}
                className="flex items-center justify-between py-1 rounded-md w-full"
            >
                <StatusBadge status={currentStatus} />
                <FaCaretDown className="w-3 h-3 ml-2" />
            </button>
            {isOpen && (
                <div className="absolute left-0 mt-1 w-48 bg-white border border-gray-300 rounded shadow-lg z-10">
                    {(status && status.length > 0 ? status : ["DONE", "IN PROGRESS", "IN REVIEW"]).map((statusOption: any) => (
                        <div
                            key={statusOption}
                            onClick={(event) => handleStatusChange(statusOption, event)}
                            className="px-4 py-2 cursor-pointer hover:bg-gray-100"
                        >
                            <StatusBadge status={statusOption} />
                        </div>
                    ))}
                </div>
            )}
        </div>
    );
};

const ProgressBar: React.FC<{ completed: number; total: number }> = ({ completed, total }) => {
    const percentage = (completed / total) * 100;

    return (
        <div className="flex items-center w-full">
            <div className="relative w-full h-6 bg-gray-200 rounded-sm overflow-hidden">
                <div
                    className="h-full bg-primary"
                    style={{ width: `${percentage}%` }}
                />
                <div className="absolute inset-0 flex items-center justify-end pr-2 text-xs font-semibold text-white">
                    {`${completed} of ${total}`}
                </div>
            </div>
        </div>
    );
};

const TableComponent: React.FC<TableComponentProps> = ({
    title,
    data,
    onRowClick,
    headers,
    isCheckBox,
    sortableColumns = {},
    togglebuttonsList,
    itemsPerPage = 3,
    onActionClick,
    onSelectionChange,
    onStatusChange,
    statusOptions
}) => {
    const [localData, setLocalData] = useState(data);
    const [sortConfig, setSortConfig] = useState<{ key: string | null; direction: 'ascending' | 'descending' }>({ key: null, direction: 'ascending' });
    const [currentPage, setCurrentPage] = useState(1);
    const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());

    useEffect(() => {
        setLocalData(data);
    }, [data]);

    // Sync selected items when data changes
    useEffect(() => {
        const validSelectedItems = new Set(
            Array.from(selectedItems).filter(id =>
                localData.some(item => item.id === id)
            )
        );
        setSelectedItems(validSelectedItems);
    }, [localData]);

    const handleStatusChange = (itemId: string, newStatus: string) => {
        const updatedData = localData.map(item =>
            item.id === itemId ? { ...item, statusdp: newStatus } : item
        );
        setLocalData(updatedData);

        if (onStatusChange) {
            onStatusChange(itemId, newStatus);
        }
    };

    const sortedData = useMemo(() => {
        let sortableData = [...localData];
        if (sortConfig.key !== null) {
            sortableData.sort((a, b) => {
                if (a[sortConfig.key!] < b[sortConfig.key!]) {
                    return sortConfig.direction === 'ascending' ? -1 : 1;
                }
                if (a[sortConfig.key!] > b[sortConfig.key!]) {
                    return sortConfig.direction === 'ascending' ? 1 : -1;
                }
                return 0;
            });
        }
        return sortableData;
    }, [localData, sortConfig]);

    const paginatedData = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage;
        return sortedData.slice(startIndex, startIndex + itemsPerPage);
    }, [sortedData, currentPage, itemsPerPage]);

    const pageCount = Math.ceil(sortedData.length / itemsPerPage);

    const requestSort = (key: string) => {
        if (!sortableColumns[key]) return;
        setSortConfig((prevConfig) => ({
            key,
            direction: prevConfig.key === key && prevConfig.direction === 'ascending' ? 'descending' : 'ascending',
        }));
    };

    const getSortIndicator = (key: string) => {
        if (sortConfig.key !== key) return null;
        return sortConfig.direction === 'ascending' ? ' ▲' : ' ▼';
    };

    const handlePageChange = (newPage: number) => {
        setCurrentPage(newPage);
    };

    const handleCheckboxChange = (itemId: string) => {
        const newSelectedItems = new Set(selectedItems);
        if (selectedItems.has(itemId)) {
            newSelectedItems.delete(itemId);
        } else {
            newSelectedItems.add(itemId);
        }
        setSelectedItems(newSelectedItems);
        onSelectionChange?.(
            sortedData.filter(item => newSelectedItems.has(item.id))
        );
    };

    const handleMenuClick = (action: string, item: any) => {

    };

    const renderPaginationButtons = () => {
        const buttons = [];
        const maxVisibleButtons = 5;
        let startPage = Math.max(1, currentPage - Math.floor(maxVisibleButtons / 2));
        let endPage = Math.min(pageCount, startPage + maxVisibleButtons - 1);

        if (endPage - startPage + 1 < maxVisibleButtons) {
            startPage = Math.max(1, endPage - maxVisibleButtons + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            buttons.push(
                <button
                    key={i}
                    onClick={() => handlePageChange(i)}
                    className={`px-3 py-1 mx-1 text-sm font-medium ${currentPage === i
                        ? 'bg-primary text-white'
                        : 'text-gray-700 hover:bg-gray-100'
                        } rounded-full transition-colors duration-200`}
                >
                    {i}
                </button>
            );
        }

        return (
            <div className="flex items-center space-x-2">
                {buttons.map((button, index) => (
                    <React.Fragment key={index}>
                        {button}
                        {index < buttons.length - 1 && (
                            <span className="text-gray-300">|</span>
                        )}
                    </React.Fragment>
                ))}
            </div>
        );
    };

    const minTableBodyHeight = `${itemsPerPage * 40}px`;

    return (
        <div className="">
            <h2 className="text-lg font-semibold mb-4">{title}</h2>
            <div className="overflow-x-auto custom-scrollbar rounded-[5px] border border-[#E9EDF5] shadow-md">
                <table className="min-w-full">
                    <thead className="bg-gradient-to-r from-[#F0F4F8] to-[#EDF2F7]">
                        <tr>
                            {isCheckBox && (
                                <th className="px-5 py-3 text-left">
                                    <input
                                        type="checkbox"
                                        className="w-5 h-5 text-primary bg-gray-100 border-gray-400 rounded focus:ring-primary focus:ring-0"
                                        onChange={(e) => {
                                            const newSelectedItems = new Set(
                                                e.target.checked ? sortedData.map(item => item.id) : []
                                            );
                                            setSelectedItems(newSelectedItems);
                                            onSelectionChange?.(
                                                sortedData.filter(item => newSelectedItems.has(item.id))
                                            );
                                        }}
                                        checked={selectedItems.size === sortedData.length}
                                    />
                                </th>
                            )}
                            {headers.map((header) => (
                                <th
                                    key={header.key}
                                    className={`px-5 py-3 text-left text-xs font-semibold text-[#4A5568] uppercase tracking-wider ${sortableColumns[header.key] ? 'cursor-pointer hover:bg-[#E2E8F0] transition-colors duration-200' : ''
                                        }`}
                                    onClick={() => requestSort(header.key)}
                                >
                                    <div className="flex items-center">
                                        {header.label}
                                        {sortableColumns[header.key] && (
                                            <span className="ml-1">{getSortIndicator(header.key)}</span>
                                        )}
                                    </div>
                                </th>
                            ))}
                        </tr>
                    </thead>
                    <tbody className="bg-white divide-y divide-[#E9EDF5]" style={{ minHeight: minTableBodyHeight }}>
                        {paginatedData.map((item) => (
                            <tr
                                key={item.id}
                                className="h-10 transition-all duration-200 ease-in-out hover:bg-[#F7FAFC] hover:shadow-sm cursor-pointer"
                                onClick={() => onRowClick(item?.id, item)}
                            >
                                {isCheckBox && (
                                    <td className="px-5 py-3" onClick={(e) => e.stopPropagation()}>
                                        <input
                                            type="checkbox"
                                            checked={selectedItems.has(item.id)}
                                            onChange={() => handleCheckboxChange(item.id)}
                                            className="w-5 h-5 text-primary bg-gray-100 border-gray-400 rounded focus:ring-primary focus:ring-0"
                                        />
                                    </td>
                                )}
                                {headers.map((header) => (
                                    <td key={header.key} className="px-5 py-3 whitespace-nowrap text-sm text-[#6B7280]" title="Click to view">
                                        {header.key === 'statusdp' ? (
                                            <StatusDropdown
                                                status={statusOptions}
                                                currentStatus={item[header.key]}
                                                onChange={(newStatus) => handleStatusChange(item.id, newStatus)}
                                            />
                                        ) : header.key === 'status' ? (
                                            <StatusBadge status={item[header.key]} />
                                        ) : header.key === 'action' && header.icon ? (
                                            <Menu as="div" className="relative inline-block text-left">
                                                <Menu.Button
                                                    className="p-2 rounded-lg transition duration-200"
                                                    onClick={(e) => e.stopPropagation()}
                                                >
                                                    {header.icon || <MoreVertical size={16} />}
                                                </Menu.Button>
                                                <Menu.Items className="absolute z-20 right-0 mt-2 w-56 origin-top-right bg-white border border-gray-200 divide-y divide-gray-100 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
                                                    {togglebuttonsList && togglebuttonsList.map((action: string, index: number) => (
                                                        <Menu.Item key={index}>
                                                            {({ active }) => (
                                                                <button
                                                                    className={`${active ? 'bg-gray-100 text-gray-900' : 'text-gray-700'
                                                                        } group flex w-full items-center px-4 py-2 text-sm`}
                                                                    onClick={(e) => {
                                                                        e.stopPropagation();
                                                                        handleMenuClick(action, item);
                                                                    }}
                                                                >
                                                                    {action}
                                                                </button>
                                                            )}
                                                        </Menu.Item>
                                                    ))}
                                                </Menu.Items>
                                            </Menu>
                                        ) : header.key === 'action' ? (
                                            <button
                                                onClick={(e) => {
                                                    e.stopPropagation();
                                                    onActionClick && onActionClick(item.id);
                                                }}
                                                className="px-3 py-1 bg-primary text-white rounded-md hover:bg-custom-primary-hover transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-opacity-50"
                                            >
                                                {header.actionLabel || 'Action'}
                                            </button>
                                        ) : header.key === 'stat' ? (
                                            <div className="flex items-center">
                                                <ProgressBar completed={item.completed} total={item.total} />
                                            </div>
                                        ) : (
                                            <div className="max-w-72 overflow-hidden whitespace-nowrap overflow-ellipsis">
                                                {item[header.key]}
                                            </div>
                                        )}
                                    </td>
                                ))}
                            </tr>
                        ))}
                        {[...Array(Math.max(0, itemsPerPage - paginatedData.length))].map((_, index) => (
                            <tr key={`empty-${index}`} className="h-10">
                                <td colSpan={headers.length + (isCheckBox ? 1 : 0)}></td>
                            </tr>
                        ))}
                    </tbody>
                </table>
            </div>
            {pageCount > 1 && (
                <div className="flex justify-end items-center mt-4 bg-[#F7FAFC] p-2 rounded-md">
                    <button
                        onClick={() => handlePageChange(currentPage - 1)}
                        disabled={currentPage === 1}
                        className="px-3 py-1 mx-1 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        &lt;
                    </button>
                    {renderPaginationButtons()}
                    <button
                        onClick={() => handlePageChange(currentPage + 1)}
                        disabled={currentPage === pageCount}
                        className="px-3 py-1 mx-1 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded-full transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                        &gt;
                    </button>
                </div>
            )}
        </div>
    );
};

export default TableComponent;