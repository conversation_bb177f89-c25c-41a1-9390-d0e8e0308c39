"use client";

import React, { useMemo, useState, useCallback, useEffect, useContext } from "react";
import Search from "@/components/BrowsePanel/TableComponents/Search";
import { ThreeDotsButton } from "../ui/ThreeDotsButton";
import TableComponent from "@/components/SimpleTable/table";
import { Filter, Plus } from "lucide-react";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import Loader from "@/components/UserOnboarding/ui/Loader"
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { fetchGroupUsers, fetchOrganizationUsers, addUsersToGroup } from "@/utils/api";
import { useParams } from "next/navigation";
import { useUser } from "@/components/Context/UserContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";


interface Member {
    id: string;
    name: string;
    email: string;
    contact_number: string;
    department: string;
    is_admin: boolean;
    status: string;
}

interface FilterState {
    status: string;
    department: string;
}

interface AllUsers {
    id: string;
    name: string;
    email: string;
}

const AddUserToGroupModal = ({users, members, onClose}: {users: AllUsers[], members: Member[], onClose: () => void}) => {
    const [selectedUsers, setSelectedUsers] = useState<string[]>([]);
    const { tenant_id } = useUser();
    const params = useParams();
    const groupId = params.id;
    const {showAlert} = useContext(AlertContext);

    const isUserMember = (userId: string) => {
        return members.some(member => member.id === userId);
    };

    const handleUserSelect = (userId: string) => {
        if (!isUserMember(userId)) {
            if (selectedUsers.includes(userId)) {
                setSelectedUsers(selectedUsers.filter(id => id !== userId));
            } else {
                setSelectedUsers([...selectedUsers, userId]);
            }
        }
    };

    const handleSelectAll = () => {
        const availableUsers = users.filter(user => !isUserMember(user.id));
        if (selectedUsers.length === availableUsers.length) {
            setSelectedUsers([]);
        } else {
            setSelectedUsers(availableUsers.map(user => user.id));
        }
    };

    const getInitials = (name: string) => {
        return name
            .split(' ')
            .map(word => word[0])
            .join('')
            .toUpperCase()
            .slice(0, 2);
    };

    const handleAddSelected = async () => {
        if (selectedUsers.length > 0) {
            try {
                const filteredUsers = selectedUsers.filter(id => !isUserMember(id));
                await addUsersToGroup(tenant_id, `${groupId}?group_id=${groupId}`, filteredUsers);
                showAlert("Users added to group successfully.", "success");
                onClose();
            } catch (err: any) {
                
                showAlert(err?.message || "Failed to add users to group.", "danger");
            }
        }
    }

    const availableUsers = users.filter(user => !isUserMember(user.id));
    const allSelected = availableUsers.length > 0 && selectedUsers.length === availableUsers.length;

    return (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 w-full max-w-md">
                <h2 className="text-xl font-semibold mb-4">All Users</h2>
                <div className="mb-4 px-4">
                    <label className="flex items-center gap-2 cursor-pointer">
                        <input
                            type="checkbox"
                            className="w-4 h-4 rounded border-gray-300"
                            checked={allSelected}
                            onChange={handleSelectAll}
                        />
                        <span className="text-sm font-medium">Select All</span>
                    </label>
                </div>
                <div className="max-h-96 overflow-y-auto">
                    {users.map((user) => {
                        const isMember = isUserMember(user.id);
                        return (
                            <div 
                                key={user.id} 
                                className={`py-3 px-4 flex items-center justify-between border-b
                                    ${isMember ? 'bg-gray-100' : 'hover:bg-gray-50'}
                                    ${selectedUsers.includes(user.id) ? 'bg-blue-50' : ''}`}
                            >
                                <div className="flex items-center gap-3">
                                    {!isMember && (
                                        <input
                                            type="checkbox"
                                            className="w-4 h-4 rounded border-gray-300"
                                            checked={selectedUsers.includes(user.id)}
                                            onChange={() => handleUserSelect(user.id)}
                                        />
                                    )}
                                    <div className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-medium
                                        ${isMember ? 'bg-gray-400' : 'bg-blue-600'}`}>
                                        {getInitials(user.name)}
                                    </div>
                                    <div className="flex flex-col">
                                        <span className="font-medium">{user.name}</span>
                                        <span className="text-sm text-gray-500">{user.email}</span>
                                    </div>
                                </div>
                                {isMember && <span className="text-sm text-gray-500">Already Member</span>}
                            </div>
                        );
                    })}
                </div>
                <div className="flex justify-end gap-4 mt-6">
                    <button 
                        className="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50"
                        onClick={() => onClose()}
                    >
                        Cancel
                    </button>
                    <button
                        className={`px-4 py-2 rounded-md
                            ${selectedUsers.length > 0 
                                ? 'bg-blue-600 text-white hover:bg-blue-700' 
                                : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                        onClick={handleAddSelected}
                        disabled={selectedUsers.length === 0}
                    >
                        Add Selected ({selectedUsers.length})
                    </button>
                </div>
            </div>
        </div>
    );
}

const GroupMembers: React.FC = () => {
    const params = useParams();
    const [isFilterOpen, setIsFilterOpen] = useState(false);
    const [searchTerm, setSearchTerm] = useState('');
    const [rowsPerPage] = useState(10);
    const [isLoading, setIsLoading] = useState(true);
    const [allUsers, setAllUsers] = useState<Member[]>([]);
    const [members, setMembers] = useState<Member[]>([]);
    const [showAllUsers, setShowAllUsers] = useState(false);
    const [filters, setFilters] = useState<FilterState>({
        status: '',
        department: ''
    });
    const { tenant_id } = useUser();

    const fetchMembers = async () => {
        try {
            const orgId = tenant_id;
            const path = window.location.pathname;
            const groupId = path.split('/groups/')[1].split('/')[0];
            // Or alternatively: path.match(/groups\/(grp-[^/]+)/)?.[1];
      
            if (!groupId) {
              
              return;
            }
            const response = await fetchGroupUsers(orgId, groupId);
            setMembers(response);
        } catch (error) {
            
        } finally {
            setIsLoading(false);
        }
    };
    useEffect(() => {
        fetchMembers();
    }, []);

    const fetchAllUsers = async () => {
            const response = await fetchOrganizationUsers(tenant_id);
            setAllUsers(response);
        }

    useEffect(() => {
        fetchAllUsers();
    }, []);

    const handleSearch = (term: string) => {
        setSearchTerm(term);
    }
    const actionButtonsData = [
        {label: "Suspend", onclick: () =>{} },
        {label: "Delete", onclick: () => {}}
      ];

    const handleFilterChange = (newFilters: FilterState) => {
        setFilters(newFilters);
    }

    
    const handleSuspend = useCallback(async (id: string) => {
        try {
            
        } catch (error) {
            
        }
    }, []);
    
    const handleDelete = useCallback(async (id: string) => {
        try {
            
        } catch (error) {
            
        }
    }, []);

    const handleFilterClick = () => {
        setIsFilterOpen(!isFilterOpen);
    };

    const FilterDropdown: React.FC<{
        filters: FilterState;
        setFilters: (filters: FilterState) => void;
        onClose: () => void;
    }> = ({ filters, setFilters, onClose }) => {
        const handleClickOutside = (e: MouseEvent) => {
            if (!(e.target as Element).closest(".filter-dropdown")) {
                onClose();
            }
        };
    
        React.useEffect(() => {
            document.addEventListener("mousedown", handleClickOutside);
            return () => document.removeEventListener("mousedown", handleClickOutside);
        }, []);
    
        return (
            <div className="absolute right-0 mt-2 w-56 bg-white rounded-lg shadow-lg z-50 py-2 filter-dropdown">
                <div className="px-4 py-2">
                    <h3 className="text-sm font-medium text-gray-900">Status</h3>
                    <div className="mt-2 space-y-2">
                        {["active", "inactive"].map((status) => (
                            <label key={status} className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={filters.status === status}
                                    onChange={() =>
                                        setFilters({
                                            ...filters,
                                            status: status === filters.status ? "" : status,
                                        })
                                    }
                                    className="rounded border-gray-300"
                                />
                                <span className="ml-2 text-sm text-gray-700">{status}</span>
                            </label>
                        ))}
                    </div>
                </div>
    
                <div className="px-4 py-2 border-t">
                    <h3 className="text-sm font-medium text-gray-900">Department</h3>
                    <div className="mt-2 space-y-2">
                        {["IT", "Development"].map((dept) => (
                            <label key={dept} className="flex items-center">
                                <input
                                    type="checkbox"
                                    checked={filters.department === dept}
                                    onChange={() =>
                                        setFilters({
                                            ...filters,
                                            department: dept === filters.department ? "" : dept,
                                        })
                                    }
                                    className="rounded border-gray-300"
                                />
                                <span className="ml-2 text-sm text-gray-700">{dept}</span>
                            </label>
                        ))}
                    </div>
                </div>
            </div>
        );
    };

    const filteredData = useMemo(() => {
        return members.filter(member => {
            const matchesSearch = Object.values(member)
                .join(' ')
                .toLowerCase()
                .includes(searchTerm.toLowerCase());

            const matchesDepartment = !filters.department || filters.department === member.department;
            const matchesStatus = !filters.status || filters.status === member.status;

            return matchesSearch && matchesDepartment && matchesStatus;
        }).map(member => ({
            id: member.id,
            name: member.name,
            email: member.email,
            contact: member.contact_number,
            department: member.department,
            is_admin: (
                <span className={`px-2 py-1 rounded-md ${member.is_admin ? 'bg-green-100 text-green-600' : 'bg-gray-100 text-gray-600'}`}>
                    {member.is_admin ? 'Admin' : 'Member'}
                </span>
            ),
            menuActions: <ThreeDotsButton
            actionButtonItems={actionButtonsData}

        />
        }));
    }, [filters, searchTerm, members, handleSuspend, handleDelete]);

    const renderContent = () => {
        if (isLoading) {
            return (
                <Loader type="table" />
            );
        }

        if(filteredData.length === 0) {
            if(searchTerm) {
                return (
                    <EmptyStateView
                        type="noSearchResults"
                        onClick={() => setSearchTerm("")}
                    />
                );
            }

            if(filters.department || filters.status) {
                return (
                    <EmptyStateView
                        type="noFilterResults"
                        onClick={() => setFilters({ status: "", department: ""})}
                    />
                );
            }

            return (
                <EmptyStateView
                    type="noMembers"
                    onClick={() => {}}
                />
            );
        }

        return (
            <TableComponent
                headers={[
                    {key: "name", label: "Name"},
                    {key: "email", label: "Email"},
                    {key: "is_admin", label: "Role"},
                    {key: "contact", label: "Contact"},
                    {key: "department", label: "Department"},
                    {key: "menuActions", label: ""}
                ]}
                data={filteredData}
                onRowClick={() => {}}
                sortableColumns={{
                    name: true,
                    email: true,
                    contact: true,
                    department: true
                }}
                itemsPerPage={rowsPerPage}
                title="Group Members"
            />
        );
    }

    const handleAddMember = () => {
        setShowAllUsers(true);
    }

    return (
        <div className="bg-white rounded-lg shadow flex flex-col h-[81vh]">
            <div className="p-4 border-b flex-shrink-0">
                <h1 className="project-panel-heading mb-5">Group Members</h1>
                <div className="flex justify-between items-center">
                    <div>
                        <Search searchTerm={searchTerm} setSearchTerm={handleSearch} />
                    </div>
                    <div className="flex gap-3">
                        <DynamicButton
                            variant="primary"
                            icon={Plus}
                            text="Add Member"
                            onClick={handleAddMember}
                        />
                        <DynamicButton
                            variant="secondary"
                            icon={Filter}
                            text="Filter"
                            onClick={handleFilterClick}
                        />
                        {isFilterOpen && (
                            <FilterDropdown
                                filters={filters}
                                setFilters={handleFilterChange}
                                onClose={() => setIsFilterOpen(false)}
                            />
                        )}
                    </div>
                </div>
            </div>

            {showAllUsers && (
                <AddUserToGroupModal users={allUsers} members={members} onClose={() =>
                     {
                        setShowAllUsers(false);
                        fetchMembers();
                     }
                    } />
            )}

            <div className="flex-grow overflow-hidden">
                <div>
                    {renderContent()}
                </div>
            </div>
        </div>
    );
}

export default GroupMembers;