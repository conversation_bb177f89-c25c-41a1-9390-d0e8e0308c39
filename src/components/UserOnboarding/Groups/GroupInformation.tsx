// components/Organizations/DetailsSection.tsx
import { LucideIcon } from "lucide-react"
import React from 'react'

interface DetailItem {
 label: string;
 value: string;
}

interface GroupInformationProps {
 title: string;
 icon: LucideIcon;
 details: DetailItem[];
 description?: string;
}

const GroupInformation: React.FC<GroupInformationProps> = ({ title, icon, details, description }) => {
 return (
   <div className="bg-white rounded-lg border border-gray-250 p-3 pb-6">
     <div className="flex items-center gap-2 mb-6 bg-gray-50 p-1">
       {React.createElement(icon, { className: "w-4 h-4 text-black" })}
       <h3 className="text-blue-600 font-medium text-sm">{title}</h3>
     </div>

     <div className="grid grid-cols-2 gap-2">
       {details.map((detail, index) => (
         <div key={index} className="grid grid-cols-[140px_1fr] gap-2 mb-4">
           <span className="text-sm text-gray-500 font-normal whitespace-nowrap">
             {detail.label}
           </span>
           <span className={
             `text-sm ${detail.label === 'Status' && detail.value === 'Active' ?
               "bg-green-100 text-green-600 px-2 py-0.5 rounded-md w-[60px]" :
               detail.label === 'Status' && detail.value === 'Inactive' ?
                 "bg-red-100 text-red-600 p-1 rounded-md w-[62px]" :
                 "text-gray-900 font-medium"}`
           }>
             {detail.value}
           </span>
         </div>
       ))}
     </div>

     {description && (
       <div className="grid grid-cols-[140px_1fr] gap-2 ">
         <span className="text-sm text-gray-500 font-normal whitespace-nowrap">
           Description
         </span>
         <span className="text-gray-900 font-medium">
           {description}
         </span>
       </div>
     )}
   </div>
 );
};

export default GroupInformation;