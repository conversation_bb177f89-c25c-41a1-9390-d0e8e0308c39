'use client';
import { useState, useEffect,useRef } from "react";
import YearMonthCalendar from "@/components/Calendar/calendar"
import { ChevronDown } from "lucide-react";

const MetricsCalendar = ({
    label,
    startDate,
    date,
    onChange,
}) => {
    const [isCalendarOpen, setIsCalendarOpen] = useState(false);
    const calendarRef = useRef(null);
    const dropdownRef = useRef(null);

    const filterSelect =  {
        appearance: "none",
        padding: "8px",
        borderRadius: "6px",
        border: "1px solid #e5e7eb",
        background: "white",
        fontSize: "14px",
        color: "#1f2937",
        cursor: "pointer",
    }

    useEffect(() => {
        const handleClickOutside = (event) => {
            if (calendarRef.current && dropdownRef.current && !calendarRef.current.contains(event.target) && !dropdownRef.current.contains(event.target)) {
                setIsCalendarOpen(false)
            }
        };
        document.addEventListener("mousedown", handleClickOutside);
        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
        };
    }, [calendarRef.current]);

    return (
        <div className="relative">
             {label}
              <div ref={dropdownRef} className="flex gap-5">
                <div
                  onClick={() => setIsCalendarOpen(!isCalendarOpen)}
                  style={filterSelect}
                  className="relative flex items-center justify-end gap-3"
                >
                  {new Date(date).toLocaleString("default", {
                    month: "short",
                    year: "numeric",
                  })}
                  <ChevronDown size={16} className={`transition-transform duration-300 ${isCalendarOpen? "rotate-180" : ''}`}/>
                </div>
                {isCalendarOpen && (
                  <div ref = {calendarRef} className="absolute z-[50] mt-10 right-[0] bg-white border-gray-200 border-2 rounded-md">
                    {startDate? 
                        <YearMonthCalendar  date={date} startDate={startDate} onChange={onChange}/> :
                        <YearMonthCalendar  date={date} onChange={onChange}/>
                    }
                  </div>
                )}
              </div>
        </div>
    )
}

export default MetricsCalendar;