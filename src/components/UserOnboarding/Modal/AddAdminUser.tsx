// components/UserOnboarding/Modal/AddAdminUser.tsx
import React, { useState, useRef, useEffect } from 'react';
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X } from 'lucide-react';

interface AddUserProps {
  isOpen: boolean;
  onClose: () => void;
  onAdd: (data: UserFormData) => Promise<void>;
}

interface UserFormData {
  name: string;
  email: string;
  role: string;
  contact: string;
}

// Add new interface for form errors
interface FormErrors {
  name?: string;
  email?: string;
  contact?: string;
}

const AddAdminUser: React.FC<AddUserProps> = ({
  isOpen,
  onClose,
  onAdd
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    role: 'Admin',
    contact: ''
  });
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [isFormValid, setIsFormValid] = useState(false);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('keydown', handleEscKey);
    }

    return () => {
      document.removeEventListener('keydown', handleEscKey);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const roles = ['Admin', 'Super Admin', 'User'];

  const handleClose = () => {
    setFormData({
      name: '',
      email: '',
      role: 'Admin',
      contact: ''
    });
    setErrors({});
    onClose();
  }

  const checkFormValid = (formData: UserFormData) => {
    if (!formData.name.trim()) {
      return false;
    } 
    else if(!formData.email.trim() || !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)){
      return false;
    }
    // else if(!formData.contact.trim() || !/^\+?[1-9][\d\s-]{9,}$/.test(formData.contact)){
    //   return false;
    // }

    return true;
  }

  // Update the handleInputChange function to handle validation
  const handleInputChange = (field: keyof UserFormData | 'all') => (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = field === 'email' ? e.target.value.toLowerCase() : e.target.value;
    const newFormData = {
      ...formData,
      [field]: value
    };
    setFormData(newFormData);
    setIsFormValid(checkFormValid(newFormData));
    
    // Create a new errors object starting fresh
    const newErrors: FormErrors = { ...errors };
    
    // Name validation
    if (field === 'name' || field === 'all') {
      if (!newFormData.name.trim()) {
        newErrors.name = 'Name is required';
      } else {
        delete newErrors.name; // Clear error when valid
      }
    }

    // Email validation
    if (field === 'email' || field === 'all') {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!newFormData.email.trim()) {
        newErrors.email = 'Email is required';
      } else if (!emailRegex.test(newFormData.email)) {
        newErrors.email = 'Please enter a valid email';
      } else {
        delete newErrors.email; // Clear error when valid
      }
    }

    // Contact validation
    if (field === 'contact' || field === 'all') {
      if (!newFormData.contact.trim()) {
        newErrors.contact = 'Contact number is required';
      } else if (!/^\+?[1-9][\d\s-]{9,}$/.test(newFormData.contact)) {
        newErrors.contact = 'Please enter a valid contact number that does not start with 0';
      } else {
        delete newErrors.contact; // Clear error when valid
      }
    }

    setErrors(newErrors);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-lg w-full max-w-md mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <h2 className="text-xl font-semibold">Add New Admin User</h2>
          <button 
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-600"
          >
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Form Content */}
        <div className="p-6 space-y-4">
          {/* Name Input */}
          <div>
            <label className="block text-sm text-gray-700 mb-2">Name</label>
            <input
              type="text"
              value={formData.name}
              onChange={handleInputChange('name')}
              className={`w-full px-3 py-2 border ${errors.name ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder="John Doe"
            />
            {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
          </div>

          {/* Email Input */}
          <div>
            <label className="block text-sm text-gray-700 mb-2">Email</label>
            <input
              type="email"
              value={formData.email}
              onChange={handleInputChange('email')}
              className={`w-full px-3 py-2 border ${errors.email ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder="<EMAIL>"
            />
            {errors.email && <p className="mt-1 text-sm text-red-500">{errors.email}</p>}
          </div>

          {/* Role Select */}
          <div className="hidden">
            <label className="block text-sm text-gray-700 mb-2">Role</label>
            <select
              value="Admin"
              onChange={(e) => setFormData(prev => ({ ...prev, role: e.target.value }))}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {roles.map(role => (
                <option key={role} value={role}>{role}</option>
              ))}
            </select>
          </div>

          {/* Contact Input */}
          <div>
            <label className="block text-sm text-gray-700 mb-2">Contact</label>
            <input
              type="text"
              value={formData.contact}
              onChange={handleInputChange('contact')}
              className={`w-full px-3 py-2 border ${errors.contact ? 'border-red-500' : 'border-gray-300'} rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500`}
              placeholder=""
            />
            {errors.contact && <p className="mt-1 text-sm text-red-500">{errors.contact}</p>}
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-3 p-6 border-t">
          <DynamicButton
            variant="ghost"
            text="Close"
            onClick={handleClose}
            disabled={isLoading}
          />
          <DynamicButton
            variant="primary"
            text="Add Admin User"
            onClick={async () => {
              setIsLoading(true);
              try {
                await onAdd(formData);
                handleClose();
              } catch (error) {
                
              } finally {
                setIsLoading(false);
              }
            }}
            loading={isLoading}
            disabled={!isFormValid || isLoading}
          />
        </div>
      </div>
    </div>
  );
};

export default AddAdminUser;