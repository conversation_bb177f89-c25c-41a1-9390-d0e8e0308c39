// components/UserOnboarding/Modal/CreateGroup.tsx
import React, { useState , useContext} from "react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { X } from "lucide-react";
import { Toggle } from "@/components/UserOnboarding/ui/Toggle";
import { createOrganizationGroup } from "@/utils/api";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { useUser } from "@/components/Context/UserContext";

interface GroupDetailState {
  name: string;
  description: string;
  type: string;
  accessLevel: string;
}

interface PermissionState {
  projectDetails: PermissionActions;
  workItems: PermissionActions;
  requirements: PermissionActions;
  architectureDesign: PermissionActions;
  prototype: PermissionActions;
  development: PermissionActions;
  documentation: PermissionActions;
}

interface PermissionActions {
  create: boolean;
  read: boolean;
  update: boolean;
  delete: boolean;
  merge: boolean;
}

interface CreateGroupModalProps {
  isOpen: boolean;
  onClose: () => void;
  orgId: string;
  onSuccess: () => void; // Add this prop
}

const GROUP_TYPES = [
  { label: 'Administrative', value: 'Administrative' },
  { label: 'Department', value: 'Department' },
  { label: 'Sales', value: 'Sales' },
  { label: 'Engineering', value: 'Engineering' },
  { label: 'IT Operations', value: 'IT_Operations' },
  { label: 'Human Resources', value: 'Human_Resources' },
  { label: 'Finance', value: 'Finance' },
  { label: 'Marketing', value: 'Marketing' },
  { label: 'Customer Support', value: 'Customer_Support' },
  { label: 'Research and Development', value: 'RnD' },
  { label: 'Quality Assurance', value: 'QA' },
  { label: 'Product Management', value: 'Product_Management' },
  { label: 'Legal', value: 'Legal' },
  { label: 'Operations', value: 'Operations' },
  { label: 'Project Management', value: 'Project_Management' },
  { label: 'Business Development', value: 'Business_Development' },
  { label: 'Supply Chain', value: 'Supply_Chain' },
  { label: 'Security', value: 'Security' },
  { label: 'Training', value: 'Training' },
  { label: 'Executive', value: 'Executive' }
] as const;

// If you need the type definition
type GroupType = typeof GROUP_TYPES[number]['value'];

interface Group {
  id: string;
  name: string;
  type: GroupType;
  description: string;
  memberCount: number;
}

// Add validation interface similar to AddAdminUser
interface ValidationErrors {
  name?: string;
  description?: string;
  type?: string;
  accessLevel?: string;
}

const CreateGroupModal: React.FC<CreateGroupModalProps> = ({
  isOpen,
  onClose,
  orgId,
  onSuccess
}) => {
  const [currentStep, setCurrentStep] = useState<"groupDetail" | "permissions">("groupDetail");
  const [isLoading, setIsLoading] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const { tenant_id } = useUser();

  const [groupDetail, setGroupDetail] = useState<GroupDetailState>({
    name: "",
    description: "",
    type: "",
    accessLevel: "",
  });

  const [permissions, setPermissions] = useState({
    projectDetails: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    workItems: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    requirements: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    architectureDesign: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    prototype: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    development: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    },
    documentation: {
      create: false,
      read: false,
      update: false,
      delete: false,
      merge: false
    }
  });

  const [errors, setErrors] = useState<ValidationErrors>({});
  const [isFormValid, setIsFormValid] = useState(false);

  const handleOverlayClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const areAllFieldsFilled = (): boolean => {
    return (
      groupDetail.name.trim() !== '' &&
      groupDetail.description.trim() !== '' &&
      groupDetail.type !== '' &&
      groupDetail.accessLevel !== '' &&
      Object.keys(errors).length === 0
    );
  };

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    let modifiedValue = name === "name" ? value.replace(/\s+/g, '-') : value;
    
    const newGroupDetail = {
      ...groupDetail,
      [name]: modifiedValue
    };
    setGroupDetail(newGroupDetail);
    
    const newErrors = { ...errors };
    
    // Required field validation
    if (!modifiedValue.trim()) {
      newErrors[name as keyof ValidationErrors] = 'This field is required';
      setIsFormValid(false);
    } else {
      // Additional field-specific validations
      switch (name) {
        case 'name':
          if (modifiedValue.length < 3) {
            newErrors.name = 'Group name must be at least 3 characters';
          } else if (modifiedValue.length > 50) {
            newErrors.name = 'Group name must be less than 50 characters';
          } else {
            delete newErrors.name;
          }
          break;

        case 'description':
          if (modifiedValue.length > 500) {
            newErrors.description = 'Description must be less than 500 characters';
          } else {
            delete newErrors.description;
          }
          break;

        default:
          delete newErrors[name as keyof ValidationErrors];
      }
    }

    setErrors(newErrors);
    
    // Update form validity
    const updatedGroupDetail = { ...groupDetail, [name]: modifiedValue };
    setIsFormValid(
      updatedGroupDetail.name.trim() !== '' &&
      updatedGroupDetail.description.trim() !== '' &&
      updatedGroupDetail.type !== '' &&
      updatedGroupDetail.accessLevel !== '' &&
      Object.keys(newErrors).length === 0
    );
  };

  const handlePermissionToggle = (
    feature: keyof PermissionState,
    action: keyof PermissionActions
  ) => {
    setPermissions((prev) => ({
      ...prev,
      [feature]: {
        ...prev[feature],
        [action]: !prev[feature][action],
      },
    }));
  };

  const handleNext = async () => {
    if (currentStep === "groupDetail") {
      if (!isFormValid) {
        showAlert("Please fix all validation errors", "error");
        return;
      }
      setCurrentStep("permissions");
    } else {
      setIsLoading(true);
      try {
        const payload = {
          name: groupDetail.name,
          description: groupDetail.description,
          group_type: groupDetail.type,
          access_level: groupDetail.accessLevel,
          permissions: permissions
        };

        const orgId = tenant_id;

        await createOrganizationGroup(orgId, payload);
        showAlert("Group created successfully!", "success");
        onSuccess();
        onClose();
      } catch (error) {
        showAlert("Error creating group. Please try again.","danger");
        
        // Show error message to user
      } finally {
        setIsLoading(false);
      }
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" onClick={handleOverlayClick}>
      <div className="bg-white rounded-lg w-full max-w-[1000px] mx-4 flex flex-col h-[90vh]">
        {/* Header */}
        <div className="flex flex-col p-6 border-gray-200">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Create Group</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>

          {/* Progress Indicator */}
          <div className="flex items-center">
            <div className={`flex items-center ${currentStep === "groupDetail" ? "text-blue-600" : "text-gray-500"}`}>
              <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2">
                {currentStep === "permissions" ? (
                  <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M19.1272 7.89384L18.2355 7.00102C18.0469 6.81332 17.9436 6.56305 17.9436 6.29789V5.03464C17.9436 3.39201 16.6071 2.05527 14.9648 2.05527H13.7017C13.4406 2.05527 13.1844 1.949 12.9997 1.76428L12.107 0.871465C10.9453 -0.290488 9.0567 -0.290488 7.89495 0.871465L7.0003 1.76428C6.81561 1.949 6.55943 2.05527 6.29828 2.05527H5.03525C3.39291 2.05527 2.0564 3.39201 2.0564 5.03464V6.29789C2.0564 6.56305 1.95313 6.81332 1.76547 7.00102L0.872803 7.89284C0.309801 8.45594 0 9.20476 0 10.0002C0 10.7957 0.310793 11.5446 0.872803 12.1067L1.76447 12.9995C1.95313 13.1872 2.0564 13.4374 2.0564 13.7026V14.9659C2.0564 16.6085 3.39291 17.9452 5.03525 17.9452H6.29828C6.55943 17.9452 6.81561 18.0515 7.0003 18.2362L7.89296 19.13C8.47384 19.71 9.23642 20 9.99901 20C10.7616 20 11.5242 19.71 12.1051 19.129L12.9977 18.2362C13.1844 18.0515 13.4406 17.9452 13.7017 17.9452H14.9648C16.6071 17.9452 17.9436 16.6085 17.9436 14.9659V13.7026C17.9436 13.4374 18.0469 13.1872 18.2355 12.9995L19.1272 12.1077C19.6892 11.5446 20 10.7967 20 10.0002C20 9.20376 19.6902 8.45594 19.1272 7.89384ZM14.5229 8.84028L8.56519 12.8128C8.39738 12.925 8.20475 12.9796 8.0141 12.9796C7.75792 12.9796 7.50372 12.8803 7.31209 12.6886L5.32618 10.7024C4.93794 10.3141 4.93794 9.68642 5.32618 9.29811C5.71443 8.9098 6.34197 8.9098 6.73022 9.29811L8.14021 10.7083L13.4207 7.18773C13.8785 6.88284 14.4941 7.00598 14.7979 7.46282C15.1028 7.91966 14.9796 8.53639 14.5229 8.84028Z" fill="#1C64F2"/>
                  </svg>
                ) : (
                  <div className="w-6 h-6" />
                )}
              </div>
              <span>Group Detail</span>
            </div>
            <div className="flex-1 mx-4 border-t border-gray-300" />
            <div className={`flex items-center ${currentStep === "permissions" ? "text-blue-600" : "text-gray-500"}`}>
              <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2">
                <div className="w-6 h-6" />
              </div>
              <span>Permissions</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4 custom-scrollbar">
          {currentStep === "groupDetail" ? (
            <div className="space-y-4 p-6 -mt-4">
              <div>
                <label className="block text-sm font-medium mb-2">Group Name</label>
                <input
                  type="text"
                  name="name"
                  value={groupDetail.name}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md ${
                    errors.name ? 'border-red-500' : 'border-gray-200'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Enter Group Name"
                />
                {errors.name && <p className="mt-1 text-sm text-red-500">{errors.name}</p>}
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Description</label>
                <textarea
                  name="description"
                  value={groupDetail.description}
                  onChange={handleInputChange}
                  className={`w-full px-3 py-2 border rounded-md ${
                    errors.description ? 'border-red-500' : 'border-gray-200'
                  } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  placeholder="Enter group description"
                  rows={3}
                />
                {errors.description && <p className="mt-1 text-sm text-red-500">{errors.description}</p>}
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">Group Type</label>
                  <select
                    name="type"
                    value={groupDetail.type}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.type ? 'border-red-500' : 'border-gray-200'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  >
                    <option value="">Select type</option>
                    {GROUP_TYPES.map(({ label, value }) => (
                      <option key={value} value={value}>
                        {label}
                      </option>
                    ))}
                  </select>
                  {errors.type && <p className="mt-1 text-sm text-red-500">{errors.type}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Access Level</label>
                  <select
                    name="accessLevel"
                    value={groupDetail.accessLevel}
                    onChange={handleInputChange}
                    className={`w-full px-3 py-2 border rounded-md ${
                      errors.accessLevel ? 'border-red-500' : 'border-gray-200'
                    } focus:outline-none focus:ring-2 focus:ring-blue-500`}
                  >
                    <option value="">Select access</option>
                    <option value="Admin">Admin</option>
                    <option value="Member">Member</option>
                    <option value="Viewer">Viewer</option>
                  </select>
                  {errors.accessLevel && <p className="mt-1 text-sm text-red-500">{errors.accessLevel}</p>}
                </div>
              </div>
            </div>
          ) : (
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr>
                    <th className="px-6 py-4 text-left font-medium border-t border-l border-b border-gray-200">Features (Phases)</th>
                    <th className="px-6 py-4 text-center font-medium border-t border-b border-l border-gray-200">Create</th>
                    <th className="px-6 py-4 text-center font-medium border-t border-b border-l border-gray-200">Read</th>
                    <th className="px-6 py-4 text-center font-medium border-t border-b border-l border-gray-200">Update</th>
                    <th className="px-6 py-4 text-center font-medium border-t border-b border-l border-gray-200">Delete</th>
                    <th className="px-6 py-4 text-center font-medium border border-gray-200">Merge/Approval</th>
                  </tr>
                </thead>
                <tbody>
                  {Object.entries(permissions).map(([feature, actions]) => (
                    <tr key={feature}>
                      <td className="px-6 py-4 text-left border-b border-l border-gray-200">
                        {feature.replace(/([A-Z])/g, " $1").trim()}
                      </td>
                      {Object.entries(actions).map(([action, value], index, array) => (
                        <td 
                          key={action} 
                          className={`px-6 py-4 border-b border-l border-gray-200 ${
                            index === array.length - 1 ? 'border-r' : ''
                          }`}
                        >
                          <div className="flex justify-center items-center">
                            <Toggle
                              enabled={value}
                              onChange={() => handlePermissionToggle(
                                feature as keyof PermissionState,
                                action as keyof PermissionActions
                              )}
                              size="small"
                            />
                          </div>
                        </td>
                      ))}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 p-6">
          <div className="flex justify-end gap-4">
            {currentStep === "permissions" && (
              <DynamicButton
                variant="ghost"
                text="Back"
                onClick={() => setCurrentStep("groupDetail")}
              />
            )}
            <DynamicButton 
              variant="ghost" 
              text="Close" 
              onClick={onClose} 
            />
            <DynamicButton
              variant="primary"
              text={currentStep === "groupDetail" ? "Next: Permissions" : "Create Group"}
              onClick={handleNext}
              isLoading={isLoading}
              disabled={currentStep === "groupDetail" ? !isFormValid : false}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default CreateGroupModal;