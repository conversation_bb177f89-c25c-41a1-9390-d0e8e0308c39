// components/UserOnboarding/ui/Alert.tsx
import React from 'react';
import { AlertCircle, CheckCircle2, Info } from 'lucide-react';

type AlertType = 'success' | 'error' | 'warning' | 'info';

interface AlertProps {
  type: AlertType;
  message: string;
}

const Alert: React.FC<AlertProps> = ({ type, message }) => {
  const getAlertStyles = () => {
    switch (type) {
      case 'success':
        return {
          containerClass: 'bg-green-50 border-green-200',
          textClass: 'text-green-800',
          icon: <CheckCircle2 className="h-4 w-4 text-green-600" />
        };
      case 'error':
        return {
          containerClass: 'bg-red-50 border-red-200',
          textClass: 'text-red-800',
          icon: <AlertCircle className="h-4 w-4 text-red-600" />
        };
      case 'warning':
        return {
          containerClass: 'bg-amber-50 border-amber-200',
          textClass: 'text-amber-800',
          icon: <AlertCircle className="h-4 w-4 text-amber-600" />
        };
      case 'info':
        return {
          containerClass: 'bg-orange-50 border-orange-200',
          textClass: 'text-orange-800',
          icon: <Info className="h-4 w-4 text-orange-600" />
        };
    }
  };

  const styles = getAlertStyles();

  return (
    <div className={`flex items-center gap-2 px-4 py-3 rounded-md border ${styles.containerClass}`}>
      {styles.icon}
      <span className={`text-sm ${styles.textClass}`}>{message}</span>
    </div>
  );
};

export default Alert;