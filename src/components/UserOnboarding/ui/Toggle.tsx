// components/UIComponents/Toggle/Toggle.tsx
import React from 'react';

interface ToggleProps {
  enabled: boolean;
  onChange: (enabled: boolean) => void;
  size?: 'small' | 'medium' | 'large';
  label?: string;
  disabled?: boolean;
}

export const Toggle: React.FC<ToggleProps> = ({ 
  enabled, 
  onChange, 
  size = 'medium',
  label,
  disabled = false
}) => {
  const sizeClasses = {
    small: 'h-4 w-8',
    medium: 'h-6 w-11',
    large: 'h-8 w-14'
  };

  const toggleClasses = {
    small: 'h-3 w-3',
    medium: 'h-4 w-4',
    large: 'h-6 w-6'
  };

  const translateClasses = {
    small: enabled ? 'translate-x-4' : 'translate-x-1',
    medium: enabled ? 'translate-x-6' : 'translate-x-1',
    large: enabled ? 'translate-x-8' : 'translate-x-1'
  };

  return (
    <div className="flex items-center gap-2">
      {label && (
        <span className={`text-sm ${disabled ? 'text-gray-400' : 'text-gray-700'}`}>
          {label}
        </span>
      )}
      <button
        type="button"
        className={`
          ${sizeClasses[size]}
          ${enabled ? 'bg-blue-600' : 'bg-gray-200'}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
          relative inline-flex items-center rounded-full transition-colors focus:outline-none
        `}
        onClick={() => !disabled && onChange(!enabled)}
        disabled={disabled}
        aria-pressed={enabled}
      >
        <span className="sr-only">{label || 'Toggle'}</span>
        <span
          className={`
            ${toggleClasses[size]}
            ${translateClasses[size]}
            inline-block transform rounded-full bg-white transition-transform duration-200
          `}
        />
      </button>
    </div>
  );
};