// src/components/ui/Card.tsx
interface CardProps {
    title?: string;
    children: React.ReactNode;
    className?: string;
  }
  
  export const Card = ({ title, children, className = '' }: CardProps) => {
    return (
      <div className={`bg-white rounded-lg shadow-sm ${className}`}>
        {title && (
          <div className="px-6 py-4 border-b">
            <h3 className="text-lg font-medium text-gray-900">{title}</h3>
          </div>
        )}
        <div className="p-6">
          {children}
        </div>
      </div>
    );
  };