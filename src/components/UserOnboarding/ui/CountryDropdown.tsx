import React from "react";
import { ChevronDown } from "lucide-react";
import { COUNTRY_CODES } from "@/constants/userOnboarding/countryCodes";

interface CountryDropdownProps {
    value? : string;
    onChange: (e: React.ChangeEvent<HTMLSelectElement>) => void;
    onFocus?: () => void;
    onBlur? : () => void;
}

const CountryDropdown : React.FC<CountryDropdownProps> = ({value, onChange, onFocus, onBlur}) => {

  const countries = COUNTRY_CODES;

  return (
    <div className="w-full h-full rounded-md relative">
        <div className="w-full max-h[100%] h-full gap-1 flex justify-end items-center text-lg">
            {value}
            <ChevronDown size={16} />
        </div>

      <select
        id="country"
        className="block h-full w-full inset-0 opacity-0 py-2 opacity-1 px-3 border-2 absolute border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm"
        value={value}
        onChange={onChange}
        onFocus={onFocus}
        onBlur={onBlur}
      >
        {countries.map((country) => (
          <option key={country.code} value={country.code}>
            {country.name} ({country.code})
          </option>
        ))}
      </select>

    </div>
  );
};

export default CountryDropdown;
