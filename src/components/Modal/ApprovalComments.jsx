import { useState, useEffect, useCallback, useRef, useContext, memo } from "react";
import { Send, X, Pencil, Trash2, MessageSquareText, NotepadText, CircleCheck } from "lucide-react";
import {
    getCommentsForDiscussion,
    addOrEditCommentToDiscussion,
    deleteCommentFromDiscussion,
} from "@/utils/discussionAPI";
import { formatDateTime } from "@/utils/datetime";
import Cookies from "js-cookie";
import { DynamicButton } from "../UIComponents/Buttons/DynamicButton";
import { AlertContext } from "../NotificationAlertService/AlertList";

// Memoized CommentItem component
const CommentItem = memo(({ 
    comment, 
    isEditing, 
    onEdit, 
    onSave, 
    onDelete, 
    canModify 
}) => {
    const [editedComment, setEditedComment] = useState(comment.comment);
    const [isDeleting, setIsDeleting] = useState(false);

    const getInitials = useCallback(() => {
        const nameParts = comment.name.split(" ");
        return nameParts.length > 0
            ? nameParts.map((part) => part.charAt(0).toUpperCase()).join("")
            : comment.initials.toUpperCase();
    }, [comment.name, comment.initials]);

    const getInitialsColor = useCallback(() => {
        const hash = comment.name.split('').reduce((acc, char) => ((acc << 5) - acc) + char.charCodeAt(0), 0);
        const hue = hash % 360;
        return `hsl(${hue}, 80%, 60%)`;
    }, [comment.name]);

    const handleSubmit = async (e) => {
        e.preventDefault();
        if (editedComment.trim()) {
            await onSave(editedComment);
        }
    };

    const handleDelete = async () => {
        setIsDeleting(true);
        try {
            await onDelete();
        } finally {
            setIsDeleting(false);
        }
    };

    return (
        <div className="bg-gray-50 p-4 rounded-lg space-y-3 transition-all hover:bg-gray-100">
            <div className="flex items-start justify-between gap-3">
                <div className="flex items-center gap-3">
                    <div 
                        className="h-10 w-10 rounded-full flex items-center justify-center text-white font-medium"
                        style={{ backgroundColor: getInitialsColor() }}
                    >
                        {getInitials()}
                    </div>
                    <div>
                        <p className="font-medium text-sm">{comment.name}</p>
                        <time className="text-xs text-gray-500">
                            {formatDateTime(comment.created_at)}
                        </time>
                    </div>
                </div>

                {canModify && (
                    <div className="flex gap-1">
                        <DynamicButton
                            type="button"
                            size="sqSmall"
                            variant="ghostPrimary"
                            icon={Pencil}
                            onClick={onEdit}
                            disabled={isDeleting}
                        />
                        <DynamicButton
                            type="button"
                            size="sqSmall"
                            variant="ghostDanger"
                            icon={Trash2}
                            onClick={handleDelete}
                            disabled={isDeleting}
                        />
                    </div>
                )}
            </div>

            {isEditing ? (
                <form onSubmit={handleSubmit} className="space-y-3">
                    <textarea
                        value={editedComment}
                        onChange={(e) => setEditedComment(e.target.value)}
                        className="w-full px-3 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 resize-none min-h-[60px]"
                        rows={2}
                    />
                    <div className="flex gap-2">
                        <DynamicButton
                            type="submit"
                            size="small"
                            variant="primary"
                            text="Save changes"
                            disabled={!editedComment.trim()}
                        />
                    </div>
                </form>
            ) : (
                <p className="text-sm text-gray-700 whitespace-pre-wrap">{comment.comment}</p>
            )}
        </div>
    );
});

CommentItem.displayName = 'CommentItem';

// Main component
export default function ApprovalComments({
    title,
    discussionId,
    modificationIndex,
    description,
    setIsOpen,
    isOpen,
}) {
    const [comments, setComments] = useState([]);
    const [newComment, setNewComment] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [isSending, setIsSending] = useState(false);
    const [editingCommentIndex, setEditingCommentIndex] = useState(null);
    const [error, setError] = useState("");

    const username = Cookies.get("username");
    const userId = Cookies.get("userId");
    const textareaRef = useRef(null);
    const commentsEndRef = useRef(null);
    const { showAlert } = useContext(AlertContext);

    const scrollToBottom = useCallback(() => {
        commentsEndRef.current?.scrollIntoView({ behavior: "smooth" });
    }, []);

    const fetchComments = useCallback(async () => {
        if (!isOpen || !discussionId || modificationIndex === undefined) return;

        try {
            setError("");
            setIsLoading(true);
            const result = await getCommentsForDiscussion(discussionId, modificationIndex);
            setComments(result.comments);
        } catch (error) {
            setError("Failed to load comments. Please try again.");
            
        } finally {
            setIsLoading(false);
        }
    }, [discussionId, modificationIndex, isOpen]);

    useEffect(() => {
        fetchComments();
    }, [fetchComments]);

    useEffect(() => {
        if (comments.length > 0) {
            scrollToBottom();
        }
    }, [comments.length, scrollToBottom]);

    const handleAddComment = async (e) => {
        e.preventDefault();
        if (!newComment.trim() || isSending) return;

        setIsSending(true);
        try {
            setError("");
            await addOrEditCommentToDiscussion(discussionId, modificationIndex, newComment.trim());
            setNewComment("");
            if (textareaRef.current) {
                textareaRef.current.style.height = "auto";
            }
            await fetchComments();
        } catch (error) {
            setError("Failed to add comment. Please try again.");
            
        } finally {
            setIsSending(false);
        }
    };

    const getInitials = useCallback(() => {
        const nameParts = username ? username.split(" ") : ["Unknown"];
        return nameParts.map((part) => part.charAt(0).toUpperCase()).join("");
    }, [username]);

    const getInitialsColor = useCallback(() => {
        const hashName = username || "Unknown";
        const hash = hashName.split('').reduce((acc, char) => ((acc << 5) - acc) + char.charCodeAt(0), 0);
        const hue = hash % 360;
        return `hsl(${hue}, 80%, 60%)`;
    }, [username]);

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
            <div className="fixed inset-0 bg-black/50 backdrop-blur-sm" onClick={() => setIsOpen(false)}></div>
            <div className="relative w-full max-w-2xl mx-4 max-h-[74vh] overflow-auto bg-white shadow-xl rounded-lg">
                <div className="border-b p-4 flex items-center justify-between bg-white">
                    <div className="flex items-center gap-3 w-full">
                        <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                            {/* <FaRegCircleCheck className="h-4 w-4 text-blue-600" /> */}
                            <CircleCheck className="h-4 w-4 text-blue-600" />
                        </div>
                        <h3 className="text-lg font-semibold flex-1">{title}</h3>
                        <button
                            onClick={() => setIsOpen(false)}
                            className="p-2 hover:bg-gray-100 rounded-full transition-colors ml-auto"
                        >
                            <X className="h-5 w-5 text-gray-500" />
                        </button>
                    </div>
                </div>

                <div className="p-6 space-y-6">
                    {error && (
                        <div className="bg-red-50 border border-red-200 text-red-600 px-4 py-3 rounded-lg text-sm">
                            {error}
                        </div>
                    )}

                    {description && (
                        <section className="bg-gray-50 p-4 rounded-lg space-y-2">
                            <div className="flex items-center gap-2 text-gray-700">
                                {/* <PiNotepadBold className="h-5 w-5" /> */}
                                <NotepadText className="h-5 w-5" />
                                <h2 className="font-semibold">Description</h2>
                            </div>
                            <p className="text-sm text-gray-600 pl-7">{description}</p>
                        </section>
                    )}

                    <section className="space-y-4">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center gap-2 text-gray-700">
                                {/* <LiaComments className="h-5 w-5" /> */}
                                <MessageSquareText className="h-5 w-5" />

                                <h2 className="font-semibold">Comments</h2>
                            </div>
                            <span className="text-sm text-gray-500">
                                {comments.length} {comments.length === 1 ? 'comment' : 'comments'}
                            </span>
                        </div>

                        <div className="space-y-4  overflow-y-auto custom-scrollbar">
                            {isLoading ? (
                                <div className="space-y-4">
                                    {[1, 2, 3].map((i) => (
                                        <div key={i} className="animate-pulse">
                                            <div className="flex items-center gap-3 mb-3">
                                                <div className="w-10 h-10 bg-gray-200 rounded-full" />
                                                <div className="space-y-2">
                                                    <div className="h-4 w-24 bg-gray-200 rounded" />
                                                    <div className="h-3 w-16 bg-gray-200 rounded" />
                                                </div>
                                            </div>
                                            <div className="h-16 bg-gray-200 rounded" />
                                        </div>
                                    ))}
                                </div>
                            ) : comments.length === 0 ? (
                                <div className="text-center py-12 bg-gray-50 rounded-lg">
                                    {/* <EnParser content={en.NoComments} /> */}
                                    <p className="text-sm text-gray-400 mt-2">Be the first to comment</p>
                                </div>
                            ) : (
                                <>
                                    {comments.map((comment, index) => (
                                        <CommentItem
                                            key={index}
                                            comment={comment}
                                            isEditing={editingCommentIndex === index}
                                            onEdit={() => setEditingCommentIndex(index)}
                                            onSave={async (newContent) => {
                                                try {
                                                    await addOrEditCommentToDiscussion(
                                                        discussionId,
                                                        modificationIndex,
                                                        newContent,
                                                        null
                                                    );
                                                    await fetchComments();
                                                    setEditingCommentIndex(null);
                                                } catch (error) {
                                                    setError("Failed to edit comment");
                                                }
                                            }}
                                            onDelete={async () => {
                                                try {
                                                    await deleteCommentFromDiscussion(
                                                        discussionId,
                                                        modificationIndex,
                                                        index
                                                    );
                                                    await fetchComments();
                                                } catch (error) {
                                                    setError("Failed to delete comment");
                                                }
                                            }}
                                            canModify={userId === comment.user_id}
                                        />
                                    ))}
                                    <div ref={commentsEndRef} />
                                </>
                            )}
                        </div>

                        <form onSubmit={handleAddComment} className=" w-full inline-flex items-center space-x-3 pt-4 border-t">
                            <div className="h-10 w-10 rounded-full flex items-center justify-center text-white font-medium" style={{ backgroundColor: getInitialsColor() }}>
                                {getInitials()}
                            </div>

                            <div className="relative inline-flex items-center space-x-3 flex-1">
                                <textarea
                                    ref={textareaRef}
                                    value={newComment}
                                    onChange={(e) => {
                                        setNewComment(e.target.value);
                                        e.target.style.height = 'auto';
                                        e.target.style.height = `${Math.min(e.target.scrollHeight, 120)}px`;
                                    }}
                                    placeholder="Write a comment..."
                                    className="w-full px-4 py-2 text-sm border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 pr-12 min-h-[42px] max-h-[56px] resize-none"
                                    rows={1}
                                />
                                <DynamicButton
                                    type="submit"
                                    size="sqSmall"
                                    disabled={!newComment.trim() || isSending}
                                    isLoading={isSending}
                                    variant="primary"
                                    icon={Send}
                                />

                            </div>
                        </form>
                    </section>
                </div>
            </div>
        </div>
    );
}