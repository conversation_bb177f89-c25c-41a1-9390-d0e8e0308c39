import React from "react";
import Image from 'next/image';
import kavia<PERSON>ogo from "@/../public/logo/kavia_logo.svg";

const LoginSignupModal = ({ handleClick, setIsModalOpen }) => {
  return (
    <>
      <div className="fixed z-50 inset-0 top-0 flex items-center justify-center bg-black bg-opacity-50">
        <div className="w-96 h-3/4 relative bg-[#231F20] rounded-lg shadow-lg">
          <button
            type="button"
            className="absolute top-3 end-2.5 text-gray-400 bg-transparent hover:bg-gray-800 hover:text-gray-200 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center"
            onClick={() => setIsModalOpen(false)}
          >
            <svg
              className="w-3 h-3"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 14 14"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m1 1 6 6m0 0 6 6M7 7l6-6M7 7l-6 6"
              />
            </svg>
            <span className="sr-only">Close modal</span>
          </button>
          <div className="p-12 py-16 text-center flex flex-col justify-around items-center">
            <Image
              src={kaviaLogo}
              alt={"Kavia AI"}
              className={"size-24"}
            />
            <h3 className="my-8 text-sm font-normal text-gray-500">
              To use Kavia you must log into an existing account or create one using one of the options below
            </h3>
            <button
              type="button"
              className="w-full text-white bg-orange-600 hover:bg-orange-800 focus:ring-1 focus:outline-none focus:ring-orange-300 font-medium rounded-lg text-md px-5 py-2.5 text-center"
              onClick={() => handleClick('/users/sign_up')}
            >
              Sign Up
            </button>
            <button
              type="button"
              className="mt-4 w-full text-white bg-gray-600 hover:bg-gray-800 focus:ring-1 focus:outline-none focus:ring-orange-300 font-medium rounded-lg text-md px-5 py-2.5 text-center"
              onClick={() => handleClick('/users/login')}
            >
              Login
            </button>
          </div>
        </div>
      </div>
    </>
  );
};

export default LoginSignupModal;
