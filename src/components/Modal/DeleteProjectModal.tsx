import React from "react";
import { X, Trash2 } from "lucide-react";
import { DynamicButton } from "@/components/UIComponents/Buttons/DynamicButton";
import { createPortal } from "react-dom";

type DeleteModalType =
    | "project"
    | "task"
    | "requirements"
    | "discussion"
    | "user"
    | "notification"
    | "Multiple Requirements"
    | "status"
    | "section"
    | "document";

interface DeleteProjectModalProps {
    isOpen: boolean;
    onClose: () => void;
    onDelete: () => void;
    isDeleting: boolean;
    type: DeleteModalType;
}

const DeleteProjectModal: React.FC<DeleteProjectModalProps> = ({
    isOpen,
    onClose,
    onDelete,
    isDeleting,
    type,
}) => {
    const getTextContent = (): string => {
        switch (type) {
            case "project":
                return "Are you sure you want to delete this project from Kavia?";
            case "task":
                return "Are you sure you want to delete this task from Kavia?";
            case "requirements":
                return "Are you sure you want to delete this requirement from Kavia?";
            case "discussion":
                return "Are you sure you want to delete this discussion from Kavia?";
            case "user":
                return "Are you sure you want to delete this user from Kavia?";
            case "notification":
                return "Are you sure you want to delete this notification from Kavia?";
            case "Multiple Requirements":
                return "Are you sure you want to delete these requirements from Kavia?";
            case "status":
                return "Are you sure you want to cancel code generation?";
            case "section":
                return "Are you sure you want to delete this section from the document?";
            case "document":
                return "Are you sure you want to delete this document? This action cannot be undone.";
                
            default:
                return "Are you sure you want to perform this action?";
        }
    };

    const getTextWarning = (): string => {
        switch (type) {
            case "project":
                return "By deleting this project, other related data will also be permanently removed";
            case "task":
                return "By deleting this task, other related data will also be permanently removed";
            case "requirements":
                return "By deleting these requirements, other related data will also be permanently removed";
            case "discussion":
                return "By deleting this discussion, other related data will also be permanently removed";
            case "user":
                return "By deleting this user, other related data will also be permanently removed";
            case "notification":
                return "By deleting this notification, other related data will also be permanently removed";
            case "Multiple Requirements":
                return "By deleting these requirements, other related data will also be permanently removed";
            case "status":
                return "Stopping code generation will discard any progress made so far. Do you still want to cancel?";
            case "section":
                return "By deleting this section, all its content will be permanently removed";
            case "document":
                return "By deleting this document, all related data and references will be permanently removed.";
                
            default:
                return "By performing this action, other related data will also be permanently removed";
        }
    };

    if (!isOpen) return null;

    return (
        createPortal(
            <div className="fixed inset-0 z-50 flex items-center justify-center overflow-y-auto">
                {/* Backdrop */}
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 transition-opacity"
                    onClick={onClose}
                />

                {/* Modal */}
                <div className="relative bg-white rounded-lg shadow-xl w-full max-w-md mx-4 overflow-hidden">
                    {/* Header */}
                    <div className="flex items-center justify-between p-4 border-b">
                        <h2 className="text-xl font-semibold text-gray-900">
                            {type === "status" ? "Cancel Code Generation" : `Delete ${type}`}
                        </h2>
                        <DynamicButton
                            variant="ghost"
                            size="sqSmall"
                            icon={X}
                            onClick={onClose}
                            tooltip="Close"
                        />
                    </div>

                    {/* Content */}
                    <div className="p-4 space-y-4">
                        <p className="text-gray-600">{getTextContent()}</p>

                        {/* Warning Alert */}
                        <div className="flex items-start p-4 rounded-lg bg-red-50 text-red-800">
                            <p className="ml-3 text-sm">{getTextWarning()}</p>
                        </div>
                    </div>

                    {/* Footer */}
                    <div className="flex justify-end gap-3 px-4 py-3 bg-gray-50">
                        <DynamicButton
                            variant="secondary"
                            onClick={onClose}
                            text="Cancel"
                        />
                        <DynamicButton
                            variant="danger"
                            icon={Trash2}
                            isLoading={isDeleting}
                            disabled={isDeleting}
                            onClick={onDelete}
                            text={type === "status" ? "Stop" : "Delete"}
                        />
                    </div>
                </div>
            </div>
        , document.body)
    );
};

export default DeleteProjectModal;