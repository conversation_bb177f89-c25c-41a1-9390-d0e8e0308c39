import React from "react";
import {
  MessageSquareOff,
  MessagesSquare,
  BellOff,
  ReceiptText,
  Users,
  CircleDollarSign,
  HelpCircle,
  ClipboardList,
  Info,
  ListChecks,
  MonitorDot,
  LayoutList,
  FolderPlus,
  FileCode,
  SearchCheck,
  ListTodo,
  Unplug,
  WandSparkles,
  Layers,
  ListCollapse,
  LayoutPanelTop,
  BookOpenText,
  Blocks,
  ListOrdered,
  CloudOff,
  GitBranch,
  Files,
  Globe,
  CodeXml,
  FolderGit2,
  FileOutput,
  Gitlab,
  Github,
  Bug,
  Figma,
  Component,
  Waypoints,
  Building,
  SearchX,
  FilterX,
  PackageX,
  Lock,
  GitPullRequestClosed,
  History,
  SearchCode
} from "lucide-react";

const EmptyStateView = ({ type = "default", className = "", onClick =()=>{}, isLoading = false }) => {
  const config = {
    chat: {
      icon: MessageSquareOff,
      title: "No Messages Yet",
      message: "Start a conversation or wait for new messages to appear here.",
      actionText: "Start a conversation",
    },
    notifications: {
      icon: BellOff,
      title: "All Caught Up!",
      message:
        "You're up to date with all your notifications. Check back later for updates.",
      actionText: "Check settings",
    },
    featureNotImplemented: {
      icon: FilterX,
      title: "Feature Not Available",
      message:
        "This feature is not available right now. Please check back later.",
      actionText: "Go Back",
    },
    discussions: {
      icon: MessagesSquare,
      title: "No Active Discussions",
      message:
        "Join or start a discussion to collaborate with your team members.",
      actionText: "Start discussion",
    },
    code_sessions: {
      icon: CodeXml,
      title: "No Sessions Found",
      message:
        "Start a new code maintenance session to manage your repositories."
    },
    query_sessions: {
      icon: SearchCode,
      title: "No Query Sessions Found",
      message:
        "Start a new query session to manage your repositories."
    },
    repositories: {
      icon: GitPullRequestClosed,
      title: "No Repositories Found",
      message:
        'Add repositories using the "Add Repository" button to start querying the codebase.',
      actionText: "Add Repository",
    },
    queries: {
      icon: MessagesSquare,
      title: "No Active Queries",
      message: "Start a query to learn more about any repository.",
      actionText: "Start discussion",
    },
    tasks: {
      icon: ListChecks,
      title: "No Tasks Yet",
      message:
        "Great job! You've completed all your tasks. New tasks will appear here.",
      actionText: "Create task",
    },
    noSearchResult: {
      icon: SearchCheck,
      title: "No Results Found",
      message:
        "No messages match your search criteria. Please try different keywords.",
      actionText: "Clear search",
    },
    noGroups: {
      icon: PackageX,
      title: "No Groups Found",
      message:
        "Get started by creating your first group to organize your team members.",
      actionText: "Create Group",
    },
    noPermissions: {
      icon: Lock, // Import Lock from lucide-react
      title: "No Permissions Set",
      message: "This group doesn't have any permissions configured yet.",
      actionText: "Configure Permissions",
    },
    noOrganizations: {
      icon: Building,
      title: "No Organizations Yet",
      message:
        "There are no organizations added to your account yet. Start by adding your first organization.",
      actionText: "Add Organization",
    },
    noSearchResults: {
      icon: SearchX,
      title: "No Users Found",
      message:
        "We couldn't find any users matching your search. Try different keywords or clear your search.",
      actionText: "Clear search",
    },
    noDocumentSearchResults: {
      icon: SearchX,
      title: "No Documents Found",
      message:
        "We couldn't find any documents matching your search term. Try different keywords or check your spelling.",
      actionText: "Clear search",
    },
    noFilterResults: {
      icon: FilterX,
      title: "No Filtered Results",
      message:
        "No organizations match your current filters. Try adjusting your filter criteria to see more results.",
      actionText: "Clear filters",
    },
    noDeploymentFound: {
      icon: CloudOff,
      title: "No Deployment Details",
      message:
        "There are currently no deployment details available for this project.",
      actionText: "View Details",
    },
    noSubscriptionData: {
      icon: CircleDollarSign,
      title: "No Subscription Information Available",
      message: "We couldn't find any active subscription for your account. This might happen if you don't have an active subscription or there was an issue connecting to the billing service.",
      actionText: "Refresh Billing Information",
    },

    noScmProviders: {
      icon: GitBranch,
      title: "No SCM Providers Details",
      message:
        "There are currently no scm providers  details available at this moment.",
      actionText: "View Details",
    },

    noGitlabConfig: {
      icon: Gitlab,
      title: "No Gitlab SCM Configuration Details",
      message:
        "There are currently no Gitlab scm configuration  details available at this moment.",
      actionText: "View Details",
    },
    noGithubConfig: {
      icon: Github,
      title: "No GitHub SCM Configuration Details",
      message:
        "There are currently no GitHub scm configuration  details available at this moment.",
      actionText: "View Details",
    },
    noOutputContent: {
      icon: Globe, // or FileOutput if you prefer
      title: "No Content Available",
      message: "Please wait while the workspace is being prepared...",
      actionText: "Check Status",
    },

    workspaceError: {
      icon: CloudOff,
      title: "Unable to Connect",
      message:
        "The server is currently unavailable. This usually happens when the workspace is starting up. Please wait a moment and try again.",
      actionText: "Refresh",
    },

    noTaskCountResults: {
      icon: ListChecks,
      title: "No Tasks Found",
      message:
        "It looks like there are no tasks available at the moment. Check back later or try adding a new task.",
      actionText: "Clear filters",
    },
    networkError: {
      icon: CloudOff,
      title: "Project Not Started",
      message:
        "The project is not running on this port yet. Please start the project and try again.",
      actionText: "Refresh",
    },
    noLLMSearchResults: {
      icon: SearchCheck,
      title: "No Matching Results",
      message:
        "Try different project ID or agent name to find what you're looking for.",
      actionText: "Clear search",
    },
    noLLMCostData: {
      icon: CircleDollarSign,
      title: "No Cost Details Found",
      message: "No cost activity found in this time period.",
      actionText: "Change date",
    },
    projects: {
      icon: FolderPlus,
      title: "No Projects Yet",
      message: "Create your first project to get started with your team.",
      actionText: "Create project",
    },
    noFrames: {
      icon: Figma,
      title: "No Frames Found",
      message:
        "No frames found in your Figma file. Please create a new frame to get started.",
      actionText: "Create Frame",
    },

    workItems: {
      icon: ListTodo,
      title: "No Work Items Yet",
      message:
        "It looks like you haven't created any work items yet. Start by adding your first work item to get organized!",
      actionText: "Create task",
    },
    requirements: {
      icon: ListChecks,
      title: "No Requirements Yet",
      message:
        "It seems you haven't added any requirements yet. Start by adding your first requirement to clarify your project's goals!",
      actionText: "Add Requirement",
    },
    noMembers: {
      icon: Users,
      title: "No Members Found",
      message:
        "There are currently no members in this group. Add members to get started.",
      actionText: "Add Members",
    },
    userStory: {
      icon: ClipboardList,
      title: "No User Stories Yet",
      message:
        "It looks like no user stories have been created yet. Start by adding your first user story to help define your project requirements!",
      actionText: "Create User Story",
    },

    epic: {
      icon: Layers,
      title: "No Epics Yet",
      message:
        "It looks like no epics have been created yet. Start by adding your first epic to organize and structure your project's work at a higher level!",
      actionText: "Create Epic",
    },
    
    task: {
      icon: ListChecks,
      title: "No Tasks Yet",
      message:
        "It looks like no tasks have been created yet. Start by adding your first task to keep your project on track!",
      actionText: "Create Task",
    },
    noUsers: {
      icon: Users,
      title: "No Users Yet",
      message:
        "It looks like there are no users here yet. Start by inviting team members or adding users to collaborate effectively!",
      actionText: "Add User",
    },
    designtab: {
      icon: WandSparkles,
      title: "No Design Items Yet",
      message:
        "It looks like no design items have been created yet. Start by adding your first design item to get your project started!",
      actionText: "Create design",
    },
    designDetails: {
      icon: ListCollapse,
      title: "No Design Details  Yet",
      message:
        "It looks like no design details  have been created yet. Start by adding your first design details to get your project started!",
      actionText: "Create design details",
    },
    subComponents: {
      icon: Component,
      title: "No SubComponents Yet",
      message:
        "It looks like no subcomponents have been added yet. Start by adding your first subcomponent to build out your project structure!",
      actionText: "Add subcomponent",
    },
    containers: {
      icon: LayoutPanelTop,
      title: "No Containers Yet",
      message:
        "It looks like no containers have been created yet. Start by adding your first container to organize your project!",
      actionText: "Create container",
    },

    containerDetails: {
      icon: LayoutPanelTop,
      title: "No Container Details Yet",
      message:
        "It looks like no container details have been created yet. Your container details will appear here once added !",
      actionText: "Create container",
    },

    interfaces: {
      icon: Waypoints,
      title: "No Interfaces Found",
      message: "No interfaces have been created for this container yet.",
      actionText: "Add Interface",
    },
    relatedDesignChildNodes: {
      icon: LayoutList,
      title: "No Related Designs Found",
      message:
        "It seems there are no related design child nodes yet. Add your first design node to enhance your architectural documentation!",
      actionText: "Add Design Node",
    },
    designTypeDetails: {
      icon: BookOpenText,
      title: "No Design Type Details Available",
      message:
        "It seems there are currently no design type details created. Begin by adding your first design type to get your project moving!",
      actionText: "Add Design Type",
    },
    architectureRequirementsChildNodes: {
      icon: LayoutList,
      title: "No Child Nodes Available",
      message:
        "There are currently no child nodes under architecture requirements. Add your first child node to detail your architectural needs!",
      actionText: "Create Child Node",
    },
    architectureRequirementsNotFound: {
      icon: ListChecks,
      title: "No Architecture Requirements Available",
      message:
        "There are currently no  architecture requirements. Start by adding your first requirement to clarify your project's goals!",
      actionText: "Create Architecture Requirements",
    },
    systemContext: {
      icon: MonitorDot,
      title: "No System Context Yet",
      message:
        "It looks like no system contexts have been created yet. Start by adding your first system context to enhance your project organization!",
      actionText: "Create system context",
    },
    components: {
      icon: Layers,
      title: "No Components Yet",
      message:
        "It looks like no components have been created yet. Start by adding your first component to build your project!",
      actionText: "Create component",
    },

    componentsDetails: {
      icon: Layers,
      title: "No Component Details Yet",
      message:
        "It looks like no component details have been created yet. Your components details will appear here once added!",
      actionText: "Create component",
    },
    architectureInterfaces: {
      icon: Unplug,
      title: "No Architecture Interfaces Available",
      message:
        "No architecture interfaces have been added yet. Interface details will appear here once available.",
      actionText: "Add Interface",
    },
    sadDocumentation: {
      icon: FileCode,
      title: "No SAD Documents Added",
      message:
        "Looks like you haven't added any SAD documents yet. Your SAD documentation will appear here once added.",
      actionText: "Create SAD Documentation ",
    },
    noDocumentsFound: {
      icon: FileCode,
      title: "No  Documents Added",
      message:
        "Looks like you haven't added any  documents yet. Your  documentation will appear here once added.",
      actionText: "Add Documentation ",
    },
    prdDocumentation: {
      icon: FileCode,
      title: "No PRD Documents Added",
      message:
        "Looks like you haven't added any PRD documents yet. Your PRD documentation will appear here once added.",
      actionText: "Create PRD Documentation ",
    },
    apiDocumentation: {
      icon: FileCode,
      title: "No API Documents Added",
      message:
        "Looks like you haven't added any API documents yet. Your API documentation will appear here once added.",
      actionText: "Add API Documentation for this interface",
    },
    statusPanel: {
      icon: Info,
      title: "No Status Information Yet",
      message:
        "Status information will be displayed here once the project starts progressing. Updates on milestones and any pending actions will appear automatically.",
      actionText: "View Details",
    },

    interfaceDefinition: {
      icon: ReceiptText,
      title: "Interface Definition Not Found",
      message:
        "It seems that the required interface definition is missing or hasn't been added yet. Once added, the details will be displayed here.",
    },

    interfaceDetails: {
      icon: ReceiptText,
      title: "Interface Details",
      message:
        "It seems that the required interface details is missing or hasn't been added yet. Once added, the details will be displayed here.",
    },

    noStepsAvailable: {
      icon: ListOrdered,
      title: "No Steps Available",
      message:
        "There are currently no steps available for this project. Updates will be displayed here once the project starts progressing.",
      actionText: "View Details",
    },

    noFunctionCallsAvailable: {
      icon: GitBranch,
      title: "No Function Calls Available",
      message:
        "There are currently no function calls available for this project. Updates will be displayed here once the project starts progressing.",
      actionText: "View Details",
    },

    noFilesToDisplay: {
      icon: Files,
      title: "No Files to Display",
      message:
        "There are currently no files to display for this project. Updates will be shown here once the project starts progressing.",
      actionText: "View Details",
    },

    noBrowserFound: {
      icon: Globe,
      title: "No Browser Found",
      message: "There are currently no browsers found for this project.",
      actionText: "View Details",
    },
    noBranchesFound: {
      icon: GitBranch,
      title: "No Branches Available",
      message: "There are currently no branches available for this repository.",
      actionText: "View Details",
    },

    noTerminalFound: {
      icon: CodeXml,
      title: "No Terminal Found",
      message: "There are currently no terminals found for this project.",
      actionText: "View Details",
    },
    noRepoFound: {
      icon: FolderGit2,
      title: "No Repository Found",
      message: "There are currently no repositories found for this project.",
      actionText: "View Details",
    },

    RepoNotFound: {
      icon: FolderGit2,
      title: "No Repository Found",
      message: "Looks like you haven't added any  repositories yet. Your  repository will appear here once added.",
      actionText: "Click to add repo",
    },

    repoNotFound: {
      icon: FolderGit2,
      title: "No Repository Found",
      message: "There are currently no repositories found at this moment.",
      actionText: "View Details",
    },

    noOverview: {
      icon: ClipboardList,
      title: "No Overview Details Found",
      message:
        "Start by adding general information, key objectives, and milestones to get a clear picture of your project's overview.",
      actionText: "Add Overview",
    },
    DebugPanelEmptyState: {
      icon: Bug,
      title: "No Debug Messages Found",
      message:
        "No debug messages have been logged yet. Monitor your application to see logs appear here.",
      actionText: "Clear Console",
    },
    noOutputFound: {
      icon: FileOutput,
      title: "No Output Found",
      message:
        "No output was generated for the provided URL. Please check the URL and ensure it is valid.",
      actionText: "Check URL",
    },
    softwareArchitecture: {
      icon: FileCode,
      title: "No Software Architecture Documents Added",
      message:
        "Looks like you haven't added any architecture documents yet. Your software architecture documentation will appear here once added.",
      actionText: "Add API Endpoint",
    },
    status: {
      icon: Info,
      title: "No Status Information Yet",
      message:
        "Status information will be displayed here once the project progresses. Updates on milestones, completion percentage, and any pending actions will appear automatically.",
      actionText: "View Past Statuses",
    },
    cost: {
      icon: CircleDollarSign,
      title: "No Cost Information Yet",
      message:
        "Start a chat to generate cost details. Once initiated, estimated costs and budget information will automatically update for that project.",
      actionText: "Start Chat",
    },
    comingSoon: {
      icon: Blocks,
      title: "Feature Coming Soon",
      message:
        "This feature is coming soon! We're working on it to make it available soon.",
      actionText: "Learn More",
    },
    agentCostDistribution: {
      icon: CircleDollarSign,
      title: "No Agent Distribution Data",
      message: "No cost distribution found for this selected time period.",
    },
    phaseCostDistribution: {
      icon: CircleDollarSign,
      title: "No Phase Distribution Data",
      message: "No cost distribution found for this selected time period.",
    },
    userCostAnalysis: {
      icon: Users,
      title: "No User Cost Data",
      message: "No user cost data found for this selected time period.",
    },
    projectCostAnalysis: {
      icon: FolderPlus,
      title: "No Project Cost Data",
      message: "No project cost data found for this selected time period.",
    },
    noRecentProjectsFound: {
      icon: FolderPlus,
      title: "No Recent Projects Found",
      message:
        "No recent projects found. Please create a new project to get started.",
      actionText: "Create Project",
    },
    noPreviousVersionFound :{
      icon: History,
      title : "No Previous version data found",
      message :"Content will appear here once it's available."
    },
    noCurentVersionFound :{
      icon: History,
      title : "No Current version data found",
      message :"Content will appear here once it's available."
    },
    default: {
      icon: HelpCircle,
      title: "Nothing Here Yet",
      message: "Content will appear here once it's available.",
      actionText: "Learn more",
    },
  };

  const {
    icon: Icon,
    title,
    message,
    actionText,
  } = config[type] || config.default;

  return (
    <div className={`flex items-center justify-center p-6 ${className}`}>
      <div className="max-w-2xl w-full">
        {" "}
        {/* Increased from max-w-sm to max-w-2xl */}
        <div className="text-center space-y-8">
          {/* Icon container with gradient background */}
          <div className="relative mx-auto w-24 h-24 flex items-center justify-center">
            <div className="absolute inset-0 bg-gradient-to-tr from-gray-50 to-gray-100 rounded-full animate-pulse" />
            <Icon
              className="relative text-gray-400 w-12 h-12 transform transition-transform hover:scale-110 duration-300"
              strokeWidth={1.5}
            />
          </div>

          {/* Text content with wider message */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 tracking-tight">
              {title}
            </h3>
            <p className="text-sm leading-relaxed text-gray-500 max-w-lg mx-auto">
              {" "}
              {/* Changed from max-w-xs to max-w-lg */}
              {message}
            </p>
            {actionText && type && (type === "sadDocumentation" || type === "prdDocumentation") && (
              <div className="pt-4">
                <a
                  href="#"
                  onClick={(e) => {
                    e.preventDefault();
                    if (!isLoading && onClick) onClick();
                  }}
                  className={`inline-flex items-center text-orange-500 hover:text-orange-600 transition-colors duration-200 ${isLoading ? 'pointer-events-none opacity-70' : ''}`}
                >
                  {isLoading ? (
                    <>
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-orange-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                      Creating...
                    </>
                  ) : (
                    actionText
                  )}
                </a>
              </div>
            )}
          </div>

        </div>
        {/* Decorative elements */}
        {/*<div className="absolute inset-0 -z-10 overflow-hidden">
          <div className="absolute inset-0 bg-grid-gray-100 bg-center [mask-image:radial-gradient(circle_at_center,white,transparent_75%)]" />
        </div>*/}
      </div>
    </div>
  );
};

export default EmptyStateView;
