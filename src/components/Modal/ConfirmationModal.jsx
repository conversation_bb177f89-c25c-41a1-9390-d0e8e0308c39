"use client"
import React, { useState, useEffect, useContext} from "react";
import axios from "axios";
import { usePathname } from 'next/navigation';
import { TopBarContext } from '../Context/TopBarContext';
import { PanelContext } from "../Context/PanelContext";
import { getHeadersRaw } from "@/utils/api";

const ConfirmationModal = ({
  showModal,
  functionCallData,
  onCancel,
  onConfirm,
}) => {
  const [successMessage, setSuccessMessage] = useState("");
  const [FailureMessage, setFailureMessage] = useState("");
  const [isButtonDisabled, setIsButtonDisabled] = useState(false);
  const [formData, setFormData] = useState({});
  const { updateTabTitle,setActiveTab,tabs ,setTabs} = useContext(TopBarContext);
  const { getProject } = useContext(PanelContext);
  const pathname = usePathname();
  const [id] = pathname.split("/").slice(2); // Extract ID from URL

  const getDiscussionIdFromUrl = () => {
    if (typeof window !== 'undefined') {
      const urlParams = new URLSearchParams(window.location.search);
      return urlParams.get('discussion_id');
    }
    return null;
  };

  useEffect(() => {
    if (functionCallData && functionCallData.details) {
      const initialFormData = {};
      Object.keys(functionCallData.details).forEach((key) => {
        initialFormData[key] = functionCallData.details[key];
      });
      setFormData(initialFormData);
    }
  }, [functionCallData]);

  const handleInputChange = (key, value) => {
    setFormData((prevFormData) => ({
      ...prevFormData,
      [key]: value,
    }));
  };

  // 
  const handleConfirmation = async (confirmed) => {
    setIsButtonDisabled(true); // Disable the button when clicked
    const discussionId = getDiscussionIdFromUrl();

    let { operation, type, details } = functionCallData;
    try {
      let url = `${process.env.NEXT_PUBLIC_API_URL}/conversation/confirmation/${functionCallData.task_id}?confirm=${confirmed}`;

      // Add discussion_id to the URL if it exists
      if (discussionId) {
        url += `&discussion_id=${discussionId}`;
      }

      const response = await axios.post(
        url,
        {'node_data': formData},
        {
          headers: getHeadersRaw()
        }
      );


      if (response.status === 200) {
        // Parse the stringified JSON in response.config.data
        const parsedConfigData = JSON.parse(response.config.data);

        // Extract the "Name" field from node_data
        const updatedTitle = parsedConfigData.node_data?.Name;

        setIsButtonDisabled(false);
        setSuccessMessage("Successfully completed!");
        setTimeout(async () => { // Ensure async here
          onCancel();
          setSuccessMessage("");
          onConfirm("Executed Successfully");

          if (operation === "delete") {
            window.location.href = "/home?tab=recentprojects";
          } else if (operation === "update") {

              updateTabTitle(id, updatedTitle); // Update title
  
            window.location.reload();
          }
        }, 2000);

      } else {
        setIsButtonDisabled(false);
        
        setFailureMessage("Error in processing query");
        setTimeout(() => {
          onCancel();
          setFailureMessage("");
          onConfirm("Execution failed. Please try again");
        }, 3000);
      }
    } catch (error) {
      setIsButtonDisabled(false);
      setFailureMessage("Error in processing query");
      setTimeout(() => {
        onCancel();
        setFailureMessage("");
        onConfirm("Execution failed. Please try again");
      }, 3000);
      
    }
  };

  const renderModalContent = () => {
    const { operation, type, details } = functionCallData;
    

    switch (operation) {
      case "delete":
        return (
          <>
            <h3
              className="text-3xl leading-6 font-medium text-gray-900"
              id="modal-title"
            >
              Confirm Delete
            </h3>
            <div className="mt-4">
              <p className="mb-4">
                Are you sure you want to delete the {type} &quot;{details.Name}
                &quot; with ID &quot;{details.Id}&quot;?
              </p>
            </div>
          </>
        );
        case "update":
          return (
            <>
              <h3 className="text-3xl leading-6 font-medium text-gray-900" id="modal-title">
                Edit {type}
              </h3>
              <div className="mt-4">
              {Object.entries(details).map(([key, value]) => (
                <div key={key} className="mb-4 flex items-center">
                  <label htmlFor={key} className="block text-sm font-medium text-gray-700 w-1/3">
                    {key}
                  </label>
                  {key === "Description" || key === "Scope" ? (
                    <textarea
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-blue-500 focus:border-blue-500 block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  ) : (
                    <input
                      type="text"
                      id={key}
                      name={key}
                      value={formData[key] || ""} // Use formData for value
                      onChange={(e) => handleInputChange(key, e.target.value)} // Handle change
                      className="mt-1 focus:ring-blue-500 focus:border-blue-500 block shadow-sm sm:text-sm border-gray-300 rounded-md w-full"
                    />
                  )}
                </div>
              ))}

              </div>
            </>
          );
      case "create":
        return (
          <>
            <h3
              className="text-3xl text-center leading-6 font-medium text-gray-900"
              id="modal-title"
            >
              Create {type}
            </h3>
            <div className="mt-8">
              {Object.entries(details).map(([key, value]) => (
                <div key={key} className="mb-4 flex items-center">
                  <label
                    htmlFor={key}
                    className="block text-lg text-gray-700 w-1/3"
                  >
                    {key}
                  </label>
                  {key === "Description" ? (
                    <textarea
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-blue-500 focus:border-blue-500 block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  ) : (
                    <input
                      type="text"
                      id={key}
                      name={key}
                      value={formData[key] || ""}
                      onChange={(e) => handleInputChange(key, e.target.value)}
                      className="mt-1 focus:ring-blue-500 focus:border-blue-500 block shadow-sm sm:text-md border-gray-300 rounded-md w-full"
                      readOnly={false}
                    />
                  )}
                </div>
              ))}
            </div>
          </>
        );
      default:
        return null;
    }
  };

  const handleCancel = () => {
    // handleConfirmation(false)
    setIsButtonDisabled(false); // Enable the button when cancel is clicked
    onCancel(); // Simply close the modal without confirming
  };

  return (
    <>
      {showModal && (
        <div className="fixed top-24 z-50 inset-0 ">
          <div className="flex items-end justify-center min-h-screen pt-4 px-4 pb-20 text-center sm:block sm:p-0">
            <div
              className="fixed inset-0 bg-gray-600 opacity-75"
              aria-hidden="true"
            >
              <div className="absolute inset-0 b opacity-75"></div>
            </div>
            <div className="inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
              <div className="shadow-lg px-8 pt-8 pb-8 sm:p-10">
                {successMessage && (
                  <div className="bg-green-600 text-lg border border-green-400 text-white px-4 py-3 rounded relative mb-4">
                    <span className="block sm:inline">{successMessage}</span>
                  </div>
                )}
                {FailureMessage && (
                  <div className="bg-red-600 border text-lg border-red-400 text-white px-4 py-3 rounded relative mb-4">
                    <span className="block sm:inline">{FailureMessage}</span>
                  </div>
                )}
                <div className="mt-3 text-center sm:mt-0 sm:ml-4 sm:text-left">
                  {renderModalContent()}
                </div>

                <div className="bg-gray-50 px-4 py-3 sm:px-6 flex justify-center">
                  <button
                    type="button"
                    onClick={() => handleConfirmation(true)}
                    disabled={isButtonDisabled} // Add the disabled attribute
                    className={`mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm p-2 px-5 ${isButtonDisabled
                      ? "bg-red-400 cursor-not-allowed"
                      : "bg-red-500 hover:bg-red-600"
                      } text-lg text-white sm:mt-0 sm:ml-1 sm:w-auto`}
                  >
                    Yes
                  </button>
                  <button
                    type="button"
                    onClick={handleCancel}
                    className="mt-3 w-full inline-flex justify-center rounded-md border border-gray-300 shadow-sm p-2 px-5 bg-white text-lg text-black hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 sm:mt-0 sm:ml-3 sm:w-auto"
                  >
                    No
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </>
  );
};

export default ConfirmationModal;
