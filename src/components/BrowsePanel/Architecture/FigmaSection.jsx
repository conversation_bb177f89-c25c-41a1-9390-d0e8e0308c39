import React, { useState, useContext, useEffect } from 'react';
import { getFigmaFiles, linkFigmaComponents } from '@/utils/FigmaAPI';
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import GenericCard from '@/components/UIComponents/GenericCards/GenericCard';
import { Search, LayoutGrid, List, X, Loader2, ExternalLink, AlertCircle } from 'lucide-react';
import { format } from 'date-fns';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import { checkFigmaDesignForDeletion } from '@/utils/api';
import Cookies from 'js-cookie';

const FigmaSection = ({ designNode, projectId, onUpdate }) => {
  const [isSelectionModalOpen, setIsSelectionModalOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [figmaFiles, setFigmaFiles] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [viewMode, setViewMode] = useState('grid');
  const [isModalLoading, setIsModalLoading] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const limit_per_design = 500; // kb
  const [figmaComponents, setFigmaComponents] = useState([]);
  const [designUpdated, setDesignUpdated] = useState(false);

  const modifyExistingDesigns = async (figmaDesigns) => {
    try{
      const tenantId = Cookies.get("tenant_id")
      const response = await checkFigmaDesignForDeletion(figmaDesigns, tenantId, projectId);
      if(response){
        setFigmaComponents(response);
        setDesignUpdated(true);
      }
    }
    catch(error){
      }
  }

  useEffect(() => {
    const parsedComponents = designNode.properties?.FigmaComponents
      ? JSON.parse(designNode.properties.FigmaComponents)
      : [];
      
    if (parsedComponents.length > 0 && !designUpdated) {
      modifyExistingDesigns(parsedComponents);
    }
    else{
      setFigmaComponents(parsedComponents);
    }
  }, [designNode]); // Runs only when designNode changes

  const handleFetchingFiles = async () => {
    if(figmaFiles.length === 0){
      setIsLoading(true);
      try {
        const response = await getFigmaFiles(projectId);
        // Filter files based on size limit (convert limit_per_design from kb to mb)    
        const filteredFiles = response.designs.filter(file => 
          file.sizes?.size_kb <= limit_per_design && (file.status =="completed" || file.status == "partially_completed")
        );
        setFigmaFiles(filteredFiles);
      } catch (error) {
        showAlert("Failed to fetch Figma files", "error");
      } finally {
        setIsLoading(false);
      }
    }
    setIsSelectionModalOpen(true);
  }
  const handleFileSelect = async (file) => {
    setIsModalLoading(true);
    try {
      await linkFigmaComponents(designNode.id, file);  // Pass the entire file object
      showAlert("Figma component linked successfully", "success");
      onUpdate();
      setIsSelectionModalOpen(false);
    } catch (error) {
      showAlert("Failed to link Figma component", "error");
    } finally {
      setIsModalLoading(false);
    }
  };

  // Check if a file is already linked
  const isFileLinked = (fileUrl) => {
    return figmaComponents.some(component => component.figma_link === fileUrl);
  };

  return (
    <div className="mb-4">
      <Accordion 
        title="Linked Figma Components" 
        defaultOpen={true} 
        preview="Open this for Linked Figma Components"
        showfigma={true} 
        handleOpenModal={handleFetchingFiles} 
        isLoading={isLoading}
      >
        {figmaComponents.length > 0 ? (
          <div className="space-y-4">
            {figmaComponents.map((component) => (
            <div 
              key={component.file_key}
              className="border rounded-lg p-4 bg-white hover:shadow-md transition-shadow"
            >
              <div className={`flex justify-between items-start`}>
                <div>
                  <h3 className={`font-medium text-lg mb-1 ${component.deleted ? "opacity-50" : ""}`}>{component.name}</h3>
                  <p className={`text-sm mb-2 ${component.deleted? "text-red-500 opacity-50": "text-gray-500"}`}>
                    {component.deleted? "Deleted": `Added by ${component.created_by?.name || "Unknown"}`}
                  </p>
                  {component.sizes && (
                    <p className="text-xs text-gray-400">
                      Size: {component.sizes.size_mb.toFixed(2)} MB 
                      {component.sizes.mb_limit && ` (Limit: ${component.sizes.mb_limit} MB)`}
                    </p>
                  )}
                </div>
                <div className="flex gap-2">
                  <a 
                    href={component.figma_link}
                    target="_blank"
                    rel="noopener noreferrer"
                    className={`flex items-center gap-1 text-purple-600 hover:text-purple-700 text-sm ${component.deleted? "pointer-events-none cursor-not-allowed opacity-50": ""}`}
                  >
                    <ExternalLink size={16} />
                    Open in Figma
                  </a>
                  <button
                    onClick={async () => {
                      try {
                        let file = {
                          url: component.figma_link,
                          name: component.name,
                          sizes: component.sizes,
                          added_by: component.created_by,
                          extraction_path: component.extraction_path || "",
                        };
                        await linkFigmaComponents(designNode.id, file, true);
                        showAlert("Component unlinked successfully", "success");
                        onUpdate();
                      } catch (error) {
                        showAlert("Failed to unlink component", "error");
                      }
                    }}
                    className="text-red-600 hover:text-red-700 text-sm opacity-100"
                  >
                    Unlink
                  </button>
                </div>
              </div>
              <div className="mt-2 text-xs text-gray-400">
                Created: {format(new Date(component.created_at), 'PPp')}
                {component.updated_at && component.updated_at !== component.created_at && (
                  <span className="ml-2">
                    • Updated: {format(new Date(component.updated_at), 'PPp')}
                  </span>
                )}
              </div>
            </div>
          ))}
          </div>
        ) : (
          <p className="text-gray-600">No Figma components linked yet.</p>
        )}
      </Accordion>

      {isSelectionModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
          <div className="bg-white rounded-lg p-6 w-[800px] max-w-[90%] max-h-[90vh] overflow-y-auto">
            {/* Modal header */}
            <div className="flex justify-between items-center mb-4">
              <div>
                <h2 className="text-xl font-semibold">Select Figma File</h2>
                <div className="flex items-center gap-1 mt-1 text-sm text-gray-500">
                  <AlertCircle size={14} />
                  <span>Only designs under {limit_per_design}KB will be displayed</span>
                </div>
              </div>
              <button
                onClick={() => setIsSelectionModalOpen(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>

            {/* Search and view controls */}
            <div className="flex gap-4 mb-4">
              <div className="flex-1 relative">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" size={16} />
                <input
                  type="text"
                  placeholder="Search Figma files..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-4 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-purple-500"
                />
              </div>
              <div className="flex items-center gap-2 border rounded-md p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded ${viewMode === 'grid' ? 'bg-purple-100 text-purple-600' : 'text-gray-600'}`}
                >
                  <LayoutGrid size={20} />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded ${viewMode === 'list' ? 'bg-purple-100 text-purple-600' : 'text-gray-600'}`}
                >
                  <List size={20} />
                </button>
              </div>
            </div>

            {/* File list */}
            {isLoading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-8 w-8 animate-spin text-purple-600" />
                <p className="mt-2 text-gray-600">Loading Figma files...</p>
              </div>
            ) : viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {figmaFiles.map((file) => (
                  <GenericCard
                    key={file.id}
                    title={file.name}
                    description={
                      <div className="space-y-1">
                        <p>Added by {file.added_by?.name || 'Unknown'}</p>
                        {file.sizes && (
                          <p className="text-xs">
                            Size: {file.sizes.size_mb.toFixed(2)} MB
                            {file.sizes.mb_limit && ` (Limit: ${file.sizes.mb_limit} MB)`}
                          </p>
                        )}
                      </div>
                    }
                    id={isModalLoading ? (
                      <div className="flex items-center gap-2">
                        <Loader2 className="h-4 w-4 animate-spin" />
                        Loading...
                      </div>
                    ) : isFileLinked(file.url) ? (
                      <div className="flex items-center gap-1 text-green-600">
                        <span>Already Linked</span>
                      </div>
                    ) : (
                      "Link Component"
                    )}
                    type="Figma Design"
                    onClick={() => !isFileLinked(file.url) && handleFileSelect(file)}
                    disabled={isModalLoading || isFileLinked(file.url)}
                    className={isFileLinked(file.url) ? 'border-green-200 bg-green-50' : ''}
                  />
                ))}
              </div>
            ) : (
              <div className="flex flex-col gap-2">
                {figmaFiles.map((file) => (
                  <div
                    key={file.id}
                    className={`flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer
                      ${isFileLinked(file.url) ? 'border-green-200 bg-green-50' : ''}`}
                  >
                    <div>
                      <h3 className="font-medium">{file.name}</h3>
                      <p className="text-sm text-gray-500">Added by {file.added_by?.name || 'Unknown'}</p>
                      {file.sizes && (
                        <p className="text-xs text-gray-400">
                          Size: {file.sizes.size_mb.toFixed(2)} MB
                          {file.sizes.mb_limit && ` (Limit: ${file.sizes.mb_limit} MB)`}
                        </p>
                      )}
                    </div>
                    <button 
                      className={`px-4 py-2 rounded ${
                        isFileLinked(file.url)
                          ? 'text-green-600 bg-green-100'
                          : 'text-purple-600 hover:text-purple-700'
                      }`}
                      disabled={isModalLoading || isFileLinked(file.url)}
                      onClick={() => !isFileLinked(file.url) && handleFileSelect(file)}
                    >
                      {isModalLoading ? (
                        <div className="flex items-center gap-2">
                          <Loader2 className="h-4 w-4 animate-spin" />
                          Loading...
                        </div>
                      ) : isFileLinked(file.url) ? (
                        "Already Linked"
                      ) : (
                        "Link Component"
                      )}
                    </button>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default FigmaSection;