"use client";
import React from "react";
import classNames from "classnames";


const UserIcon = ({ initials, bgColor, width, height }) => (
  <div
    className={classNames(
      "relative flex items-center justify-center text-white rounded-full",
      `bg-${bgColor}-500`
    )}
    style={{ width: width, height: height }}
  >
    <span className="typography-caption font-bold">{initials}</span>
  </div>
);

export default UserIcon;
