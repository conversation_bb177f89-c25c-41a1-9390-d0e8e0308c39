// src/components/Figma/FigmaExtractionTabs.jsx
'use client'
import { useState, useEffect } from 'react';
import StatusTab from './ExtractionTabs/StatusTab';
import PrototypeTab from './ExtractionTabs/PrototypeTab';
import CodeViewer from '../Sandbox/Sandbox';

const FigmaExtractionTabs = ({ projectId, extractionStatus, extractionProgress, logMessages, selectedDesign, selectedFrame, onTabChange }) => {
  const [activeTab, setActiveTab] = useState('Prototype');
  const [isRefreshing, setIsRefreshing] = useState(false);

  const tabs = [
    // { id: 'Status', label: 'Status' },
    { id: 'Prototype', label: 'Prototype' },
    { id: 'Code', label: 'Code' },
    { id: 'Output', label: 'Output' }
  ];

  useEffect(() => {
    if (activeTab) {
      onTabChange?.(activeTab);
    }
  }, [activeTab, onTabChange]);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    // Implement refresh logic - could fetch updated data from API
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      
    } finally {
      setIsRefreshing(false);
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'Status':
        return <StatusTab 
                 projectId={projectId} 
                 extractionStatus={extractionStatus}
                 extractionProgress={extractionProgress}
                 logMessages={logMessages}
                 selectedFrame={selectedFrame}
               />;
      case 'Prototype':
        return <PrototypeTab 
                 projectId={projectId} 
                 selectedDesign={selectedDesign} 
                 selectedFrame={selectedFrame} 
               />;
      case 'Code':
        return <CodeViewer />
      case 'Output':
        return <CodeViewer outputPreviewOnly={true} />;
      default:
        return <div>Select a tab</div>;
    }
  };

  return (
    <div className="flex flex-col h-full">
      {/* Tabs Header */}
      <div className="border-b border-gray-200 h-10">
        <div className="flex items-center justify-between h-full px-2">
          <div className="flex h-full">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-4 h-full text-sm font-medium border-b-2 ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                } transition-colors duration-200`}
              >
                {tab.label}
              </button>
            ))}
          </div>
          {/* <DynamicButton
            type="button"
            size="small"
            variant="secondary"
            icon={RefreshCw}
            onClick={handleRefresh}
            isLoading={isRefreshing}
            disabled={isRefreshing}
            tooltip="Refresh content"
            className="mr-2"
          /> */}
        </div>
      </div>
      
      {/* Tab Content */}
      <div className="flex-1 overflow-auto p-0 h-full w-full">
        {renderTabContent()}
      </div>
    </div>
  );
};

export default FigmaExtractionTabs;