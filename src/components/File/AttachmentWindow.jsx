// AttachmentWindow.js
import React from 'react';
import FileStatus from './FileStatus';
import ImageThumbnail from './ImageThumbnail';

const AttachmentWindow = ({ files, onAdd, onClear, onClose, maxFiles, fileStatuses, onFileClick, isUploading }) => {
  // Fixed height for all file items to prevent layout shifts
  const fileItemHeight = "60px";
  const imageItemHeight = "222px";

  return (
    <div className="absolute bottom-full right-0 mb-2 bg-white border border-gray-300 rounded-lg shadow-lg p-4 w-96 sm:w-96 md:w-96 max-w-72 z-50">
      <h3 className="font-bold mb-2">Attached Files</h3>
      <div className="mb-4 max-h-60 overflow-y-auto">
        <div className="grid grid-cols-1 gap-2">
          {files.map((file, index) => (
            <div 
              key={index} 
              className="relative"
              style={{ 
                height: file.file_kind === "image" ? imageItemHeight : fileItemHeight,
                minHeight: file.file_kind === "image" ? imageItemHeight : fileItemHeight
              }}
            >
              {fileStatuses[file.file_name] === 'loading' ? (
                file.file_kind === "image" ? (
                  <div className="w-full h-full flex items-center justify-center bg-gray-100 rounded-lg">
                    <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
                  </div>
                ) : (
                  <div className="flex items-center justify-between h-full bg-gray-50 rounded p-2">
                    <div className="flex items-center">
                      <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-blue-500 mr-2"></div>
                      <span className="text-sm truncate max-w-[150px]">{file.file_name}</span>
                    </div>
                  </div>
                )
              ) : file.file_kind === "image" ? (
                <ImageThumbnail file={file} onClick={onFileClick} />
              ) : (
                <div className="flex items-center justify-between p-2 h-full bg-gray-50 rounded">
                  <div className="flex items-center">
                    <span className="text-sm mr-2">📎</span>
                    <span className="text-sm truncate max-w-[150px]">{file.file_name}</span>
                  </div>
                  <FileStatus
                    file={file}
                    status={fileStatuses[file.file_name]}
                    onClick={() => onFileClick(file)}
                  />
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
      <div className="flex justify-between">
        <button
          onClick={onAdd}
          className={`text-blue-500 hover:text-blue-700 ${
            files.length >= maxFiles || isUploading
              ? 'opacity-50 cursor-not-allowed'
              : ''
          }`}
          disabled={files.length >= maxFiles || isUploading}
        >
          + Add
        </button>
        <button 
          onClick={onClear} 
          className="text-red-500 hover:text-red-700" 
          disabled={isUploading}
        >
          Clear
        </button>
      </div>
      {isUploading && (
        <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-blue-500"></div>
        </div>
      )}
    </div>
  );
};

export default AttachmentWindow;