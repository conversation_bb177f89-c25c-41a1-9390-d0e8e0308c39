// components/Modal.js

import { Stack } from '@mui/material';
import { ChevronDown } from 'lucide-react';
import { useState } from 'react';

const ConfigStates = ({ data }) => {

  return (
    <div className="m-5">
      <div className="grid grid-cols-2 gap-8 ">
        <div className='grid grid-cols-3'>
            <p className="font-bold">Type</p>
            <p className="font-medium">Architecture</p>
        </div>
        <div></div>
        <div className="grid grid-cols-3">
          <p className="font-bold">Auto Config State</p>
          <div>
          <button className='bg-gray-200 rounded-full pl-5 pr-5 pt-1 pb-1 font-bold'>{data.autoconfig_state}</button>
          </div>
        </div>
        <div className="grid grid-cols-3">
          <p className="font-bold">Auto Config start design state</p>
          <div>
          <button className='bg-gray-200 rounded-full pl-5 pr-5 pt-1 pb-1 font-bold'>{data.configuration_state}</button>
          </div>
        </div>
        <div className="grid grid-cols-3 ">
          <p className="font-bold">Design details state</p>
          <div>
          <button className='bg-gray-200 rounded-full pl-5 pr-5 pt-1 pb-1 font-bold  p-2'>{data.design_details_state}</button>
          </div>
        </div>
        <div className="grid grid-cols-3">
          <p className="font-bold">Configuration State</p>
          <div>
          <button className='bg-gray-200 rounded-full pl-5 pr-5 pt-1 pb-1 font-bold'>{data.autoconfig_design_details_state}</button>
          </div>
        </div>
      </div>
    </div>
  );
};


const ArchitectureDetails = ({ data }) => {
  
  const [showDescription,setShowDescription] = useState(true);
  const [showDetails,setShowDetails] = useState(true);
  
  return (
    <div className="w-full">
      <div className="bg-white p-8 rounded-lg shadow-lg w-full">
        <h2 className="text-2xl font-bold mb-4">{data.Title}</h2>
        <p className="mb-4">{data.Functionality}</p>

        <ConfigStates data={data}/>
        <div className="mb-4">
          <Stack direction={'row'}>
            <ChevronDown onClick={(e)=>setShowDescription(!showDescription)} className="cursor-pointer"/>
            <h3 className="project-panel-heading"> Description</h3>
          </Stack>
          {showDescription&&(<p>{data.Description}</p>)}
        </div>

        <div className="mb-4">
          <Stack direction={'row'}>
            <ChevronDown onClick={()=>{setShowDetails(!showDetails)}} className='cursor-pointer'/>
            <h3 className="project-panel-heading">Details</h3>
          </Stack>
          {showDetails&&(<p>{data.Design_Details}</p>)}
        </div>

        <div className="mb-4">
          <h3 className="project-panel-heading">Recommended Tech Stack</h3>
          <ul className="list-disc list-inside">
            {data.Tech_Stack_Choices.map((tech, index) => (
              <li key={index}>{tech}</li>
            ))}
          </ul>
        </div>

      </div>
    </div>
  );
};

export default ArchitectureDetails;
