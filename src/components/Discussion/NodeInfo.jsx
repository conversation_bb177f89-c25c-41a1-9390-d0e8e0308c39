import React from "react";
import dynamic from "next/dynamic";
import { Accordion } from "@/components/UIComponents/Accordions/Accordion";
import Badge from "@/components/UIComponents/Badge/Badge";
import Editor from '@monaco-editor/react';

const NoSSR = dynamic(() => import("../Chart/MermaidChart"), {
  ssr: false,
});

const NodeBadge = ({ type }) => {
  if (!type) return null;
  return <Badge className="bg-blue-100 text-blue-800" type={type} />;
};

const NodeHeader = ({ title, type }) => (
  <div className="flex items-center gap-2">
    <h4 className="mb-1 project-panel-heading">{title}</h4>
    <NodeBadge type={type} />
  </div>
);

const processBoldText = (text) => {
  if (typeof text !== "string") return text;

  text = text.replace(/#+\s*(.*)/g, `<h1 class="text-sm font-bold">$1</h1>`)

  text = text.replace(/\*\*(.*?)\*\*/g, "<strong>$1</strong>");

  text = text.replace(/`(.*?)`/g, "<code>$1</code>");

  return text;
};

const formatListContent = (content) => {
  if (!content) return null;
  
  // Check if content is a string before using replace
  if (typeof content === "string") {
    content = content.replace(/\\n/g, "\n"); //sometimes llm provides \\n instead of \n
  } else {
    // If content is not a string, return early or handle appropriately
    return (
      <div className="text-gray-600">
        {content}
      </div>
    );
  }
  
  let codeBlock = false;

  // For string content that might contain lists
  if (typeof content === "string") {
    const lines = content
      .split("\n")
      .map((line) => line.trim())
      .filter(Boolean);

    // Check if content has list-like structure
    const isListContent = lines.some(
      (line) =>
        line.startsWith("-") ||
        line.startsWith("•") ||
        line.startsWith("**") ||
        /^\d+\./.test(line)
    );

    if (isListContent) {
      let currentMainPoint = "";
      let currentSubPoints = [];
      let sections = [];

      lines.forEach((line) => {
        if (
          !line.startsWith("-") &&
          !line.startsWith("•") &&
          !line.match(/^\d+\./)
        ) {
          if(line.includes("```")){
            codeBlock = true;
          }
          // If we have accumulated content, save it
          if (currentMainPoint || currentSubPoints.length > 0) {
            sections.push({
              mainPoint: currentMainPoint,
              subPoints: currentSubPoints,
            });
          }
          currentMainPoint = line;
          currentSubPoints = [];
        } else {
          const cleanedLine = line.replace(/^[-•]\s*|\d+\.\s*/, "");
          currentSubPoints.push(cleanedLine);
        }
      });

      // Add the last section
      if (currentMainPoint || currentSubPoints.length > 0) {
        sections.push({
          mainPoint: currentMainPoint,
          subPoints: currentSubPoints,
        });
      }

      let currentMermaidContent = null;
const parts = []; // Array to store all parts (text and mermaid).

sections.forEach((section) => {
  

  const processLine = (line) => {
    // Handle Mermaid content
    if (line.includes("<mermaid>")) {
      currentMermaidContent = line.replace("<mermaid>", "").trim();
    } else if (line.includes("</mermaid>")) {
      if (currentMermaidContent !== null) {
        currentMermaidContent += "\n" + line.replace("</mermaid>", "").trim();
        parts.push({ type: 'mermaid', content: currentMermaidContent.trim() });
        currentMermaidContent = null;
      }
    } else if (currentMermaidContent !== null) {
      currentMermaidContent += "\n" + line.trim();
    } 
    // Handle normal text content
    else {
      parts.push({ type: 'text', content: line.trim() });
    }
  };

  // Process the mainPoint of each section
  if (section.mainPoint) {
    section.mainPoint.split("\n").forEach(processLine);
  }
});



      return (
        <div className="space-y-4">
          {parts.map((part, partIndex) => (
      <React.Fragment key={partIndex}>
        {part.type === 'text' && (
          <div
            className="text-gray-800 font-medium"
            dangerouslySetInnerHTML={{
              __html: processBoldText(part.content),
            }}
          />
        )}
        {part.type === 'mermaid' && (
          <div className="mt-2">
            {}
            <NoSSR chartDefinition={part.content} />
          </div>
        )}
       
      </React.Fragment>
    ))}
          {sections.map((section, index) => {
            
            const content = <div key={index} className="space-y-2 ">
              {section.mainPoint && (
                <div
                  className="text-gray-800 font-medium" 
                  dangerouslySetInnerHTML={{
                    __html: processBoldText(section.mainPoint),
                  }}
                />
              )}
              {section.subPoints.length > 0 && (
                <ul className="list-disc pl-1 space-y-2 relative">
                  {section.subPoints.map((point, idx) => (
                    point.trim().startsWith("**") && (point.trim().endsWith("**") ||  point.trim().endsWith("**:")) ? (
                      <h2 
                        key={idx} 
                        className="text-gray-600 mt-3" 
                        dangerouslySetInnerHTML={{
                          __html: processBoldText(point),
                        }}
                      />
                    ) :
                    (
                      <li
                        key={idx}
                        className="text-gray-600 list-inside"
                        dangerouslySetInnerHTML={{
                          __html: processBoldText(point),
                        }}
                      />
                    )
                  ))}
                </ul>
              )}
            </div>

            return codeBlock? (<code>{content}</code>) : (<>{content}</>)
        })}
        </div>
      );
    }
  }

  // For regular text
  return (
    <div
      className="text-gray-600"
      dangerouslySetInnerHTML={{ __html: processBoldText(content) }}
    />
  );
};

const formatDefaultListContent = (content) => {
  if (!content) return null;
  let codeBlock = false;

  // For string content that might contain lists
  if (typeof content === "string") {
    const lines = content
      .split("\n")
      .map((line) => line.trim())
      .filter(Boolean);

    // Check if content has list-like structure
    const isListContent = lines.some(
      (line) =>
        line.startsWith("-") ||
        line.startsWith("•") ||
        line.startsWith("**") ||
        /^\d+\..*$/.test(line)
    );

    if (isListContent) {
      let currentMainPoint = "";
      let currentSubPoints = [];
      let sections = [];

      lines.forEach((line) => {
        const isVersionNumber = /^\d+\.\d+$/.test(line);
        if  (
          (!line.startsWith("-") &&
           !line.startsWith("•") &&
           !line.match(/^\d+\./) &&
           !isVersionNumber) ||
          isVersionNumber
        ) {

          if (line.includes("```")){
            codeBlock = true;
            return "";
          }
          // If we have accumulated content, save it
          if (currentMainPoint || currentSubPoints.length > 0) {
            sections.push({
              mainPoint: currentMainPoint,
              subPoints: currentSubPoints,
            });
          }
          currentMainPoint = line;
          currentSubPoints = [];
        } else {
          const cleanedLine = line.replace(/^[-•]\s*|\d+\.\s*/, "");
          if(cleanedLine.split(':').length - 1 == 1 && !cleanedLine.includes('+')){ //if the like has one colon (:) treat it as sub heading
            currentSubPoints.push(`##${cleanedLine}`);
          }
          else{
            currentSubPoints.push(cleanedLine);
          }
        }
      });

      // Add the last section
      if (currentMainPoint || currentSubPoints.length > 0) {
        sections.push({
          mainPoint: currentMainPoint,
          subPoints: currentSubPoints,
        });
      }

      return (
        <div className="space-y-4">
          {sections.map((section, index) => {
            const content = <div key={index} className="space-y-2 ">
              {section.mainPoint && (
                <h2
                  className="text-md" 
                  dangerouslySetInnerHTML={{
                    __html: processBoldText(section.mainPoint),
                  }}
                />
              )}
              {section.subPoints.length > 0 && (
                <ul className="list-disc pl-1 space-y-2 relative">
                  {section.subPoints.map((point, idx) => (
                    point.trim().startsWith("**") && (point.trim().endsWith("**") ||  point.trim().endsWith("**:")) ? (
                      <h2 
                        key={idx} 
                        className="text-gray-600 mt-3" 
                        dangerouslySetInnerHTML={{
                          __html: processBoldText(point),
                        }}
                      />
                    ) : point.trim().startsWith("##") ? (
                      <h2 
                        key={idx} 
                        className="text-gray-600 pt-3" 
                        dangerouslySetInnerHTML={{
                          __html: processBoldText(point.slice(2)),
                        }}
                      />
                    ) :(
                      <li
                        key={idx}
                        className="text-gray-600 list-inside"
                        dangerouslySetInnerHTML={{
                          __html: processBoldText(point),
                        }}
                      />
                    )
                  ))}
                </ul>
              )}
            </div>

            return codeBlock? (<code className="bg-gray-300">{content}</code>) : (<>{content}</>)
          })}
        </div>
      );
    }
  }

  // For regular text
  return (
    <div
      className="text-gray-600"
      dangerouslySetInnerHTML={{ __html: processBoldText(content) }}
    />
  );
};

const renderField = (key, value, uiMetaData) => {
  if (!value) return null;

  const cleanJsonString = (str) => {
    try {
      // Remove <JSON> tags and decode Unicode characters
      const cleanStr = str
        .replace(/\\u003cJSON\\u003e|<JSON>/, '')
        .replace(/\\u003c\/JSON\\u003e|<\/JSON>/, '')
        .replaceAll('<JSON>', '') // Plain <JSON>
        .replaceAll('</JSON>', '') 
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"');
      
      // Parse and stringify to format it properly
      const parsed = JSON.parse(cleanStr);
      return JSON.stringify(parsed, null, 2);
    } catch (e) {
      
      return str;
    }
  };

  // List of keys that should use Monaco Editor with their corresponding languages
const monacoEditorConfig = {
  'main_tf': 'hcl',
  'outputs_tf': 'hcl',
  'providers_tf': 'hcl',
  'variables_tf': 'hcl',
  'workflow_file': 'yaml',
  'docker_file': 'dockerfile',
  'build_spec': 'yaml',  // Adding this for build spec content
  'build_settings': 'yaml'  // Adding this if you have build settings
};

const isJsonContent = (str) => {
  // Handle null, undefined, or non-string inputs
  if (!str || typeof str !== 'string') {
    return false;
  }

  try {
    return (
      (str.includes('"openapi"') || str.includes('\\"openapi\\"')) ||
      (str.includes('<JSON>') || str.includes('\\u003cJSON\\u003e'))
    );
  } catch (error) {
    // Handle any unexpected errors during string operations
    
    return false;
  }
};


// Determine if we should use Monaco Editor
const shouldUseMonaco = key.endsWith('_tf') || 
  monacoEditorConfig[key] || 
  isJsonContent(value);

if (shouldUseMonaco) {
  const language = isJsonContent(value) ? 'json' : (monacoEditorConfig[key] || 'hcl');
  const processedValue = isJsonContent(value) ? cleanJsonString(value) : value;


  return (
    <div key={key} className="space-y-3">
      <h3 className="text-[15px] font-bold text-gray-800">
        {key}
      </h3>
      <div className="pl-4">
        <Editor
          height="400px"  // Increased height for better JSON visibility
          defaultLanguage={language}
          defaultValue={processedValue}
          theme="vs-dark"
          options={{
            readOnly: true,
            minimap: { enabled: false },
            scrollBeyondLastLine: false,
            lineNumbers: "on",
            automaticLayout: true,
            fontSize: 14,
            formatOnPaste: true,
            formatOnType: true,
            wordWrap: "on"
          }}
        />
      </div>
    </div>
  );
}


  const flattenMetadata = (metadata) => {
    const flattened = {};

    // Iterate through each top-level key (Design, ClassDiagram, Diagram, etc.)
    Object.values(metadata).forEach((section) => {
      // Merge each section's fields into the flattened object
      Object.entries(section).forEach(([key, value]) => {
        // Only add if the key doesn't already exist (first occurrence takes precedence)
        if (!flattened[key]) {
          flattened[key] = value;
        }
      });
    });

    return flattened;
  };

  const flattenedMetadata = flattenMetadata(uiMetaData);

  if (flattenedMetadata[key]?.display_type === 'mermaid_chart') {
    return (
      <div key={key} className="space-y-3">
        <h3 className="text-[15px] font-bold text-gray-800">
          {flattenedMetadata[key].Label || (
            key.charAt(0).toUpperCase() +
            key
              .slice(1)
              .replace(/([A-Z])/g, " $1")
              .trim()
          )}
        </h3>
        <div className="pl-4">
          <NoSSR chartDefinition={value} />
        </div>
      </div>
    );
  }

  // Handle rich text display type
  if (flattenedMetadata[key]?.display_type === 'rich_text') {
    return (
      <div key={key} className="space-y-3">
        <h3 className="text-[15px] font-bold text-gray-800">
          {flattenedMetadata[key].Label || (
            key.charAt(0).toUpperCase() +
            key
              .slice(1)
              .replace(/([A-Z])/g, " $1")
              .trim()
          )}
        </h3>
        <div className="pl-4">{formatListContent(value)}</div>
      </div>
    );
  }

  // Default rendering for other types
  return (
    <div key={key} className={`${key.includes("state")? "flex items-center gap-3 mt-3" : "space-y-3"}`}>
      <h3 className = "text-[15px] font-bold text-gray-800">
        {flattenedMetadata[key]?.Label || (
          key.charAt(0).toUpperCase() +
          key
            .slice(1)
            .replace(/([A-Z])/g, " $1")
            .trim()
        )}
      </h3>
      {key.includes("state")? (
        <Badge className="bg-blue-100 text-blue-800" type={value} />
      ): (
        <div className="pl-4">{formatDefaultListContent(value)}</div>
      )}
    </div>
  );
};

const renderNodeContent = (node, uiMetaData) => {
  if (!node) return null;

  // Fields to exclude from rendering
  const excludeFields = ["id", "Title", "Type", "__typename"];

  return (
    <div className="space-y-6">
      {Object.entries(node)
        .filter(([key]) => !excludeFields.includes(key))
        .map(([key, value]) => renderField(key, value, uiMetaData))}
    </div>
  );
};

const renderChildNode = (node, badge, uiMetaData) => {
  if (!node) return null;

  return (
    <div className="space-y-4 p-4 bg-gray-50 rounded-lg border border-gray-200">
      <div className="flex items-center gap-2">
        <h6 className="font-medium text-gray-700">
          {node.Title || "Untitled Node"}
        </h6>
        {badge}
      </div>
      {renderNodeContent(node, uiMetaData)}
    </div>
  );
};

const NodeInfo = React.memo(function NodeInfo({ nodeInfo, title = "Existing Information" }) {
  if (!nodeInfo?.node) return null;

  const { node, relationships, ui_metadata } = nodeInfo;
  const renderRelationships = (relationships) => {
    if (!relationships || Object.keys(relationships).length === 0) return null;

    return Object.entries(relationships).map(([relType, relNodes]) => {
      if (!Array.isArray(relNodes) || relNodes.length === 0) return null;

      return (
        <div key={relType} className="space-y-4">
          <div className="space-y-4">
            {relNodes.map((rel, index) => (
              <div key={index}>
                {renderChildNode(
                  rel.node,
                  rel.node?.Type && <NodeBadge type={rel.node.Type} />,
                  ui_metadata
                )}
              </div>
            ))}
          </div>
        </div>
      );
    });
  };

  return (
    <div className="space-y-4">
      <Accordion
        title={title}
        defaultOpen={true}
        className="bg-white shadow-sm rounded-lg"
      >
        <div className="  border-gray-200">
          <NodeHeader title={node.Title} type={node.Type} />
          {renderNodeContent(node, ui_metadata)}
        </div>
      </Accordion>

      {relationships &&
        Object.keys(relationships).filter((key) => key !== "hasChild").length >
        0 && (
          <Accordion
            title="Related Nodes"
            defaultOpen={false}
            className="bg-white shadow-sm rounded-lg"
          >
            <div className=" border-t border-gray-200">
              {renderRelationships(
                Object.fromEntries(
                  Object.entries(relationships).filter(
                    ([key]) => key !== "hasChild"
                  )
                )
              )}
            </div>
          </Accordion>
        )}

      {relationships?.hasChild && relationships.hasChild.length > 0 && (
        <Accordion
          title="Child Nodes"
          defaultOpen={false}
          className="bg-white shadow-sm rounded-lg"
        >
          <div className=" border-gray-200">
            {renderRelationships({ hasChild: relationships.hasChild })}
          </div>
        </Accordion>
      )}
    </div>
  );
});

export default NodeInfo;
