import React, { useState, useEffect } from 'react';
import { Code2, ListTodo, ChevronRight, FolderGit2, FileTextIcon, Box, ListTodoIcon, Figma, CheckCircle, RefreshCw, MonitorSpeaker } from "lucide-react";

const ProjectAssetsIcon = ({ className, size = 18, color = 'currentColor', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 14 14"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M12.334 3.00016H9.29598L7.69598 0.866829C7.57147 0.701578 7.41036 0.567424 7.22529 0.47489C7.04022 0.382356 6.83623 0.333959 6.62932 0.333496H3.66732C3.3137 0.333496 2.97456 0.473972 2.72451 0.72402C2.47446 0.974069 2.33398 1.31321 2.33398 1.66683V2.3335H1.66732C1.3137 2.3335 0.974557 2.47397 0.724509 2.72402C0.47446 2.97407 0.333984 3.31321 0.333984 3.66683V12.3335C0.333984 12.6871 0.47446 13.0263 0.724509 13.2763C0.974557 13.5264 1.3137 13.6668 1.66732 13.6668H10.334C10.6876 13.6668 11.0267 13.5264 11.2768 13.2763C11.5268 13.0263 11.6673 12.6871 11.6673 12.3335V11.6668H12.334C12.6876 11.6668 13.0267 11.5264 13.2768 11.2763C13.5268 11.0263 13.6673 10.6871 13.6673 10.3335V4.3335C13.6673 3.97987 13.5268 3.64074 13.2768 3.39069C13.0267 3.14064 12.6876 3.00016 12.334 3.00016ZM1.66732 3.66683H4.62932L5.62932 5.00016H1.66732V3.66683ZM1.66732 12.3335V6.3335H10.334V12.3335H1.66732ZM12.334 10.3335H11.6673V6.3335C11.6673 5.97987 11.5268 5.64074 11.2768 5.39069C11.0267 5.14064 10.6876 5.00016 10.334 5.00016H7.29598L5.69598 2.86683C5.57147 2.70158 5.41036 2.56742 5.22529 2.47489C5.04022 2.38236 4.83623 2.33396 4.62932 2.3335H3.66732V1.66683H6.62932L8.42932 4.06683C8.49142 4.14963 8.57194 4.21683 8.66451 4.26311C8.75708 4.3094 8.85915 4.3335 8.96265 4.3335H12.334V10.3335Z"
        fill={color}
      />
    </svg>
  );
};
const ProjectSetupIcon = ({ className, size = 18, color = 'currentColor', ...props }) => {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width={size}
      height={size}
      viewBox="0 0 16 16"
      fill="none"
      className={className}
      {...props}
    >
      <path
        d="M10.6527 6.66683C9.77332 6.66683 9.03198 7.23216 8.75732 8.01283L8.03865 8.00883C6.92598 8.0055 6.02065 7.10416 6.02065 6.00016V5.21083C6.80065 4.93483 7.36398 4.2015 7.36398 3.3335C7.36398 2.23083 6.45998 1.3335 5.34932 1.3335C4.23865 1.3335 3.33398 2.23083 3.33398 3.3335C3.33398 4.2015 3.89732 4.93483 4.67732 5.21083V10.7902C3.89732 11.0655 3.33398 11.7988 3.33398 12.6668C3.33398 13.7695 4.23798 14.6668 5.34865 14.6668C6.45932 14.6668 7.36332 13.7695 7.36332 12.6668C7.36332 11.7988 6.79998 11.0655 6.01998 10.7895V8.6535C6.58198 9.0775 7.27398 9.34016 8.03198 9.34283L8.76465 9.34683C9.04665 10.1142 9.78198 10.6668 10.652 10.6668C11.7633 10.6668 12.6673 9.7695 12.6673 8.66683C12.6673 7.56416 11.7633 6.66683 10.6527 6.66683ZM5.34932 13.3335C4.97865 13.3335 4.67798 13.0348 4.67798 12.6668C4.67798 12.2988 4.97932 12.0002 5.34932 12.0002C5.71932 12.0002 6.02065 12.2988 6.02065 12.6668C6.02065 13.0348 5.71932 13.3335 5.34932 13.3335ZM5.34932 4.00016C4.97865 4.00016 4.67732 3.70083 4.67732 3.3335C4.67732 2.96616 4.97865 2.66683 5.34865 2.66683C5.71865 2.66683 6.02065 2.96616 6.02065 3.3335C6.02065 3.70083 5.71932 4.00016 5.34932 4.00016ZM10.6527 9.3335C10.3467 9.3335 10.098 9.1255 10.0173 8.84683C10.0313 8.79416 10.0507 8.7435 10.0507 8.68683C10.0513 8.6215 10.0313 8.56216 10.014 8.50216C10.0893 8.21616 10.3407 8.00016 10.6527 8.00016C11.0233 8.00016 11.324 8.2995 11.324 8.66683C11.324 9.03483 11.0227 9.3335 10.6527 9.3335Z"
        fill={color}
      />
    </svg>
  );
};

const styles = `
  @keyframes pulsedot {
    0% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0.7);
    }
    70% {
      transform: scale(1);
      box-shadow: 0 0 0 6px rgba(239, 68, 68, 0);
    }
    100% {
      transform: scale(0.95);
      box-shadow: 0 0 0 0 rgba(239, 68, 68, 0);
    }
  }

  @keyframes wave {
    0% {
      transform: translate(-50%, -50%) scale(1);
      opacity: 0.8;
    }
    100% {
      transform: translate(-50%, -50%) scale(2);
      opacity: 0;
    }
  }

  .pulse-dot-status {
    position: absolute;
    top: -4px;
    right: -4px;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background-color: #ef4444;
    animation: pulsedot 2s infinite;
  }

  .pulse-dot-status::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background-color: #ef4444;
    animation: wave 2s infinite;
    z-index: -1;
  }
`;

function convertToSubActivities(items) {
  return items?.map(item => ({
    title: item.replace(/_/g, '-').replace('-', ' ').replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
  }));
}

const ProjectTimeline = ({ projectStatus, onRedirect, refetchData, isAutoConfigInProgress = false }) => {
  const activities = [
    {
      icon: ProjectAssetsIcon,
      title: "Project Assets",
      description: "Map available project resources",
      status: projectStatus.project_assets?.overall,
      redirect_text: "Add Assets",
      subActivities: [
        {
          icon: FolderGit2,
          title: "Add Repo",
          description: "Add your existing repository",
          status: projectStatus.project_assets?.repo,
          count: projectStatus.project_assets?.repoCount,
        },
        {
          icon: FileTextIcon,
          title: "Add Doc",
          description: "Add your project documentation",
          status: projectStatus.project_assets?.doc
        },
        {
          icon: Figma,
          title: "Add UI/UX",
          description: "Add your project ui",
          status: projectStatus.project_assets?.figma
        }
      ]
    },
    {
      icon: ProjectSetupIcon,
      title: "Project Setup",
      description: "Map your vision",
      status: projectStatus.project_setup,
    },
    {
      icon: FileTextIcon,
      title: "Requirements",
      description: "Transform needs into clear specifications",
      status: projectStatus.requirement?.overall,
      subActivities: [
        {
          icon: ListTodo,
          title: "Epics",
          description: "Design system architecture",
          status: projectStatus.requirement?.epic,
          count: projectStatus.requirement?.epicCount,
        },
        {
          icon: ListTodoIcon,
          title: "User Story",
          description: "Design system architecture",
          status: projectStatus.requirement?.userStory,
          count: projectStatus.requirement?.userStoryCount,
        }]
    },
    {
      icon: MonitorSpeaker,
      title: "Architecture",
      description: "Craft scalable system blueprints",
      status: projectStatus.architecture?.overall,
      subActivities: [
        {
          icon: Box,
          title: "Requirements",
          description: "Design system architecture",
          status: projectStatus.architecture?.requirement.length > 0 ? "completed" : "pending"
        },
        {
          icon: Box,
          title: "SystemContext",
          description: "Design system architecture",
          status: projectStatus.architecture?.systemContext.length > 0 ? "completed" : "pending"
        },
        {
          icon: Box,
          title: "Container",
          description: "Design system architecture",
          status: projectStatus.architecture?.container.length > 0 ? "completed" : "pending",
          subActivities: convertToSubActivities(projectStatus.architecture?.container)
        },
        {
          icon: Box,
          title: "Component",
          description: "Design system architecture",
          status: projectStatus.architecture?.component.length > 0 ? "completed" : "pending",
          subActivities: convertToSubActivities(projectStatus.architecture?.component)
        },
        {
          icon: Box,
          title: "Design",
          description: "Design system architecture",
          status: projectStatus.architecture?.design.length > 0 ? "completed" : "pending",
          subActivities: convertToSubActivities(projectStatus.architecture?.design)
        },
        {
          icon: Box,
          title: "Interfaces",
          description: "Design system architecture",
          status: projectStatus.architecture?.interface?.length > 0 ? "completed" : "pending",
          subActivities: convertToSubActivities(projectStatus.architecture?.interface || [])
        }
      ]
    }
    ,
    {
      icon: Code2,
      title: "Code Generation",
      description: "Create project codebase",
      status: projectStatus.codegen_status == "completed" ? "completed" : projectStatus.codegen_status,
    },
  ];

  const [lastRefreshed, setLastRefreshed] = useState(new Date());
  const hasActiveOrCompletedActivity = activities.some(activity =>
    activity.status === 'in-progress' || activity.status === 'completed'
  );

  const calculateDuration = (startTime, endTime) => {
    try {
      const start = new Date(startTime);
      const end = new Date(endTime);
      const diffMs = end.getTime() - start.getTime();
      const seconds = Math.floor(diffMs / 1000);
      const minutes = Math.floor(seconds / 60);
      const hours = Math.floor(minutes / 60);
      const days = Math.floor(hours / 24);
      const weeks = Math.floor(days / 7);
      const years = Math.floor(days / 365.25);
      const remainingWeeks = Math.floor((days % 365.25) / 7);
      const remainingDays = Math.floor(days % 7);
      const remainingHours = Math.floor(hours % 24);
      const remainingMinutes = Math.floor(minutes % 60);
      const remainingSeconds = Math.floor(seconds % 60);

      if (years >= 1) {
        return `${years}y ${remainingWeeks}w`;
      } else if (weeks >= 1) {
        return `${weeks}w ${remainingDays}d`;
      } else if (days >= 1) {
        return `${days}d ${remainingHours}h`;
      } else if (hours >= 1) {
        return `${hours}h ${remainingMinutes}m`;
      } else if (minutes >= 1) {
        return `${minutes}m ${remainingSeconds}s`;
      } else {
        return seconds < 20 ? '<20s' : `${seconds}s`;
      }
    } catch (error) {
      
      return '0';
    }
  };
  const handleRefresh = () => {
    setLastRefreshed(new Date());
    refetchData();

  };
  useEffect(() => {
    setLastRefreshed(new Date());
  }, [projectStatus]);

  React.useEffect(() => {
    const styleSheet = document.createElement("style");
    styleSheet.innerText = styles;
    document.head.appendChild(styleSheet);
    return () => styleSheet.remove();
  }, []);

  const getRefreshTime = () => {
    return calculateDuration(lastRefreshed, new Date());
  };

  // Timer for updating the UI display of time since last refresh
  useEffect(() => {
    const timer = setInterval(() => {
      setLastRefreshed(prev => new Date(prev));
    }, 15000);

    return () => clearInterval(timer);
  }, []);

  // Auto-refresh timer when autoconfiguration is in progress
  useEffect(() => {
    let autoRefreshTimer;

    if (isAutoConfigInProgress) {


      // Set up a timer that refreshes exactly every 30 seconds
      autoRefreshTimer = setInterval(() => {

        refetchData();
        setLastRefreshed(new Date());
      }, 30000); // Exactly 30 seconds
    }

    return () => {
      if (autoRefreshTimer) {
        clearInterval(autoRefreshTimer);
      }
    };
  }, [isAutoConfigInProgress, refetchData]);

  // Debug log for auto-config status
  useEffect(() => {

  }, [isAutoConfigInProgress]);

  const [expandedActivities, setExpandedActivities] = useState(() => {
    const initialState = {
      activities: activities.reduce((acc, _, index) => {
        acc[index] = false;
        return acc;
      }, {}),
      subActivities: {}
    };
    return initialState;
  });


  const toggleActivity = (index) => {
    setExpandedActivities(prev => ({
      ...prev,
      activities: {
        ...prev.activities,
        [index]: !prev.activities[index]
      }
    }));
  };

  const toggleNestedActivity = (activityIndex, subActivityIndex) => {
    setExpandedActivities(prev => ({
      ...prev,
      subActivities: {
        ...prev.subActivities,
        [`${activityIndex}-${subActivityIndex}`]: !prev.subActivities[`${activityIndex}-${subActivityIndex}`]
      }
    }));
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'completed':
        return 'bg-green-500';
      case 'in-progress':
        return 'bg-orange-500';
      case 'failed':
        return 'bg-red-500';
      default:
        return 'bg-gray-300';
    }
  };

  return (
    <div className="w-full">
      <div className="sticky top-0 bg-white/95 backdrop-blur supports-[backdrop-filter]:bg-white/75 z-10 flex justify-between items-center py-2 border-b">
        <div className="flex-1">
          <h2 className="text-sm font-hind font-semibold text-gray-700 px-4">Project Progress</h2>
        </div>
        <div className="flex items-center gap-1 px-4">
          <span className="text-xs text-gray-500">
            {getRefreshTime() === '<20s' ? 'just now' : `${getRefreshTime()} ago`}
            {/* {isAutoConfigInProgress && (
              <span className="ml-1 text-xs font-semibold bg-blue-100 text-blue-600 px-1.5 py-0.5 rounded-full">
                Auto-refresh every 30s
              </span>
            )} */}
          </span>
          <button
            onClick={handleRefresh}
            className={`p-1 hover:bg-gray-100 rounded-full transition-colors ${isAutoConfigInProgress ? 'bg-blue-50' : ''}`}
            title={isAutoConfigInProgress ? 'Auto-refresh active (every 30s)' : 'Refresh'}
          >
            <RefreshCw className={`w-4 h-4 ${isAutoConfigInProgress ? 'text-blue-500' : 'text-gray-600'}`} />
          </button>
        </div>
      </div>

      <div className="w-full px-4 md:px-6 pt-4">
        <div className="relative space-y-6 max-w-full mx-auto">
          {activities.map((activity, index) => {
            const IconComponent = activity.icon;
            const hasSubActivities = activity.subActivities && activity.subActivities.length > 0;
            const isExpanded = expandedActivities.activities[index];

            let timelineHeight = '3rem';
            if (hasSubActivities && isExpanded) {
              timelineHeight = `${activity.subActivities.reduce((height, subActivity, subIndex) => {
                let subActivityHeight = 3;
                if (subActivity.subActivities &&
                    subActivity.subActivities.length > 0 &&
                    expandedActivities.subActivities[`${index}-${subIndex}`]) {
                  subActivityHeight += subActivity.subActivities.length * 2.20;
                }
                return height + subActivityHeight;
              }, 0)}rem`;
            }

            return (
              <div key={index} className="relative">
{index !== activities.length - 1 && (
  <div
    className={`absolute left-[21px] w-0.5 ${activity.status === 'completed' ? 'bg-blue-500' : 'bg-gray-200'}`}
    style={{
      top: '2.75rem', // Adjusted starting position
      height: `calc(${timelineHeight} + 1.5rem)`, // Extended height to connect to the next icon
      transition: 'height 0.3s ease-in-out'
    }}
  />
)}
                <div
                  className={`flex items-start group ${hasSubActivities ? 'cursor-pointer' : ''} relative`}
                  onClick={() => hasSubActivities && toggleActivity(index)}
                >
                  <div className="relative flex flex-col items-center flex-shrink-0">
                    {hasSubActivities && (
                      <ChevronRight
                        className={`absolute -left-4 top-3 w-4 h-4 transition-transform duration-200 ${isExpanded ? 'transform rotate-90' : ''}`}
                      />
                    )}
                    <div className="relative z-[1] bg-white p-2 rounded-full border border-gray-200 shadow-sm">
                      {activity.status === 'in-progress' && (
                        <div
                          className="absolute -top-1 -right-1 w-3 h-3 rounded-full pulse-dot-status"
                          style={{
                            backgroundColor: '#ef4444',
                            boxShadow: '0 0 0 0 rgba(239, 68, 68, 1)'
                          }}
                        />
                      )}
                      {index === 0 && !hasActiveOrCompletedActivity && (
                        <div
                          className="absolute -top-1 -right-1 w-3 h-3 rounded-full pulse-dot-status"
                          style={{
                            backgroundColor: '#ef4444',
                            boxShadow: '0 0 0 0 rgba(239, 68, 68, 1)'
                          }}
                        />
                      )}
                      <IconComponent
                        className="w-6 h-6"
                        color={activity.status === 'completed' ? '#3B82F6' :
                          activity.status === 'in-progress' || (index === 0 && !hasActiveOrCompletedActivity) ? '#6B7280' :
                          '#A0AEC0'}
                      />
                    </div>
                  </div>

                  <div className="ml-4 flex-1 min-w-0">
                    <div className="flex items-center gap-2">
                      <h4
                        className="text-sm font-semibold text-gray-700 truncate flex-1 hover:text-blue-600 cursor-pointer"
                        onClick={(e) => {
                          if (!hasSubActivities) {
                            e.stopPropagation();
                            onRedirect(activity.title);
                          }
                        }}
                      >
                        {activity.title}
                      </h4>
                      {activity.status && (
                        <div className="flex items-center gap-2 flex-shrink-0">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(activity.status)}`}></div>
                        </div>
                      )}
                    </div>
                    <p className="text-gray-600 mt-1 italic text-xs line-clamp-2">{activity.description}</p>
                    {activity.title === "Configuration" && (
                      <div className="flex items-center gap-2 flex-wrap mt-2">
                        <button
                          className="text-sm font-medium text-blue-600 hover:text-blue-800 transition-all duration-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            onRedirect('Auto Configure');
                          }}
                        >
                          Auto Configure
                        </button>
                        <span className="text-sm text-gray-600">or</span>
                        <button
                          className="flex items-center text-sm font-medium text-blue-600 hover:text-blue-800 transition-all duration-200"
                          onClick={(e) => {
                            e.stopPropagation();
                            onRedirect('Auto Extract');
                          }}
                        >
                          Auto Extract
                          <span className="text-sm text-gray-600 ml-1">?</span>
                        </button>
                      </div>
                    )}
                  </div>
                </div>

                {hasSubActivities && (
                  <div className={`ml-7 space-y-4 mt-2.5 overflow-hidden transition-all duration-500 pl-4 ${
                    isExpanded ? 'max-h-[5000px] opacity-100' : 'max-h-0 opacity-0'
                  }`} style={{ willChange: 'max-height, opacity' }}>
                    {activity.subActivities.map((subActivity, subIndex) => {
                      const hasNestedSubActivities = subActivity.subActivities && subActivity.subActivities.length > 0;
                      const isNestedExpanded = expandedActivities.subActivities[`${index}-${subIndex}`];

                      return (
                        <div key={`sub-${index}-${subIndex}`}>
                          <div className="relative flex items-start group pl-6">
                            {hasNestedSubActivities && (
                              <ChevronRight
                                onClick={(e) => {
                                  e.stopPropagation();
                                  toggleNestedActivity(index, subIndex);
                                }}
                                className={`absolute left-0 top-[8px] w-4 h-4 transition-transform duration-200 cursor-pointer ${
                                  isNestedExpanded ? 'transform rotate-90' : ''
                                }`}
                              />
                            )}
                            <div className="flex-1 min-w-0">
                              <div className="flex items-center">
                                <div className="flex items-center justify-between w-full">
                                  <div className="flex items-center gap-2 flex-1 min-w-0">
                                    <div className="flex items-center mt-1.5 flex-1 min-w-0">
                                      <button
                                        className="text-gray-700 hover:text-blue-600 font-semibold group text-sm truncate max-w-full"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          onRedirect(subActivity.title === "Requirements" ? "ArchitectureRequirements" : subActivity.title);
                                        }}
                                      >
                                        <span className="truncate inline-block max-w-full">{subActivity.title}</span>
                                      </button>
                                      {subActivity.count > 0 && (
                                        <span className="ml-1 px-1.5 py-0.5 text-xs text-gray-500 bg-gray-100 rounded-full flex-shrink-0">
                                          {subActivity.count}
                                        </span>
                                      )}
                                    </div>
                                  </div>
                                  {subActivity.status && (
                                    <div className="flex items-center justify-end flex-shrink-0">
                                      {subActivity.status === 'completed' || hasNestedSubActivities ? (
                                        <CheckCircle className="w-4 h-4 text-green-500" />
                                      ) : (
                                        <div className={`w-2 h-2 rounded-full ${getStatusColor(subActivity.status)}`}></div>
                                      )}
                                    </div>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>

                          {hasNestedSubActivities && (
                            <div className={`space-y-4 mt-1.5 overflow-hidden transition-all duration-500 ${
                              isNestedExpanded ? 'opacity-100' : 'max-h-0 opacity-0'
                            }`}>
                              {subActivity.subActivities.map((nestedActivity, nestedIndex) => (
                                <div key={`nested-${index}-${subIndex}-${nestedIndex}`} className="flex items-start group mt-4 relative">
                                  <div className="ml-5 flex-1 pl-3 min-w-0 relative">
                                    <div className="flex items-center">
                                      <div className="flex items-center justify-between w-full">
                                        <span
                                          className="truncate text-sm flex-1 hover:text-blue-600 transition-colors duration-200"
                                          title={nestedActivity.title}
                                        >
                                          {nestedActivity.title}
                                        </span>
                                        {nestedActivity.status === 'completed' && (
                                          <div className="flex items-center justify-end flex-shrink-0">
                                            <div className={`w-2 h-2 rounded-full ${getStatusColor('completed')}`}></div>
                                          </div>
                                        )}
                                      </div>
                                    </div>
                                  </div>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
};

export default ProjectTimeline;