'use client';

import { useState } from 'react';
import { useParams, useSearchParams } from 'next/navigation';
import { UploadDialog } from './UploadDialog';
import {
  Search,          // same name
  Grid as GridView,
  List,            // same name
  X as Close,
  Upload,          // same name
  Pencil as <PERSON>
} from 'lucide-react';
import { useRouter } from 'next/navigation';

export function DocumentHeader({
  viewMode,
  onViewModeChange,
  onSearch,
  currentType,
  onUploadSuccess,
  currentSearch
}) {
  const [isUploadOpen, setIsUploadOpen] = useState(false);
  const searchParams = useSearchParams();
  const router = useRouter();
  const params = useParams();
  const projectId = params.projectId;

  const handleSearchChange = (e) => {
    onSearch?.(e.target.value);
  };

  const navigateToEditor = () => {
    if (currentType=="SAD"){

      router.push(`/project/${projectId}/architecture/software-architecture`);
    }
  };

  return (
    <div className="h-16 border-b border-gray-200 px-6 flex items-center justify-between bg-white">
      {/* Search Section */}
      <div className="flex-1 max-w-md">
        <div className="relative flex items-center">
          <Search className="absolute left-3 text-gray-400" size={20} />
          <input
            type="text"
            value={currentSearch}
            onChange={handleSearchChange}
            placeholder="Search documents..."
            className="w-full h-10 pl-12 pr-10 text-sm bg-gray-50
                     border border-gray-300 rounded-lg
                     focus:ring-1 focus:ring-blue-300 focus:border-blue-300
                     transition duration-150 ease-in-out
                     placeholder-gray-400"
          />
          {currentSearch && (
            <button
              onClick={() => onSearch?.('')}
              className="absolute right-3 p-1 rounded-full text-gray-400
                       hover:text-gray-600 hover:bg-gray-200
                       transition-colors duration-150 ease-in-out"
            >
              <Close size={20} />
            </button>
          )}
        </div>
      </div>

      {/* Actions Section */}
      <div className="flex items-center space-x-4 ml-4">
        <div className="flex items-center bg-gray-100 rounded-lg p-1">
          <button
            onClick={() => onViewModeChange?.('grid')}
            className={`p-2 rounded-md transition-colors duration-150 ease-in-out
                     ${viewMode === 'grid'
                       ? 'bg-white text-blue-600 shadow-sm'
                       : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'}`}
            aria-label="Grid view"
          >
            <GridView size={20} />
          </button>
          <button
            onClick={() => onViewModeChange?.('list')}
            className={`p-2 rounded-md transition-colors duration-150 ease-in-out
                     ${viewMode === 'list'
                       ? 'bg-white text-blue-600 shadow-sm'
                       : 'text-gray-600 hover:text-gray-900 hover:bg-gray-200'}`}
            aria-label="List view"
          >
            <List size={20} />
          </button>
        </div>

        {/* Added Edit Button */}
        <button
  onClick={navigateToEditor}
  disabled={!currentType}
  className={`px-4 py-2 rounded-lg text-sm font-medium
             transition-colors duration-150 ease-in-out
             inline-flex items-center space-x-2 border
             ${!currentType
               ? 'border-gray-200 text-gray-400 cursor-not-allowed'
               : 'border-blue-500 text-blue-500 hover:bg-blue-50 hover:text-blue-600 hover:border-blue-600 active:bg-blue-100'}`}
>
  <Edit size={20} />
  <span>Update</span>
</button>

        <button
          onClick={() => setIsUploadOpen(true)}
          disabled={!currentType}
          className={`px-4 py-2 rounded-lg text-sm font-medium
                   transition-colors duration-150 ease-in-out
                   inline-flex items-center space-x-2
                   ${!currentType
                     ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                     : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800'}`}
        >
          <Upload size={20} />
          <span>Upload</span>
        </button>
      </div>

      <UploadDialog
        open={isUploadOpen}
        onClose={() => setIsUploadOpen(false)}
        currentType={currentType}
        onUploadSuccess={onUploadSuccess}
      />
    </div>
  );
}