import React, { useState } from "react";
import { Menu } from "@headlessui/react";
import { <PERSON>a<PERSON><PERSON><PERSON>, FaEllipsisH, FaExclamation } from "react-icons/fa";
import { createPortal } from "react-dom";

interface AccountCardProps {
    title?: string;
    description?: string;
    isConnected: boolean;
    id: string;
    lastSynced: string;
    isMenu?: boolean;
    togglebuttonsList?: string[];
    handleMenuClick?(item?: string): void;
    onCardClick?(cardData: AccountCardData): void;
}

interface AccountCardData {
    title?: string;
    description?: string;
    isConnected: boolean;
    id: string;
    lastSynced: string;
}

const AccountCard: React.FC<AccountCardProps> = ({
    title,
    description,
    isConnected,
    id,
    lastSynced,
    togglebuttonsList,
    handleMenuClick,
    onCardClick,
}) => {
    const _handleMenuClick = (item?: string) => {
        handleMenuClick && handleMenuClick(item);
    };

    const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });

    const handleMenuOpen = (event: React.MouseEvent) => {
        const rect = event.currentTarget.getBoundingClientRect();
        setMenuPosition({ top: rect.top, left: rect.left });
    };

    const cardData: AccountCardData = {
        title,
        description,
        isConnected,
        id,
        lastSynced,
    };

    return (
        <div
            className="relative max-w-lg mx-auto bg-white shadow-md rounded-lg p-4 mb-4 hover:shadow-lg hover:bg-gray-50 hover:scale-105 transform transition duration-200"
            onClick={(e) => {
                onCardClick && onCardClick(cardData)
            }}
        >
            <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-gray-800">{title}</h2>

                <div className="flex items-center space-x-2">
                    {isConnected ? (
                        <div className="bg-green-500 w-5 h-5 rounded-full flex items-center justify-center">
                            <FaCheck size={10} color="white" />
                        </div>
                    ) : (
                        <div className="bg-red-500 w-5 h-5 rounded-full flex items-center justify-center">
                            <FaExclamation size={10} color="white" />
                        </div>
                    )}

                    {togglebuttonsList && (
                        <Menu as="div" className="relative">
                            <Menu.Button
                                className="p-2 rounded-lg border border-gray-300 shadow-sm hover:shadow-md bg-white transition duration-200"
                                onClick={(e) => {
                                    e.stopPropagation();
                                    handleMenuOpen(e);
                                }}
                            >
                                <FaEllipsisH size={12} />
                            </Menu.Button>
                            {createPortal(
                                <Menu.Items
                                    className="absolute bg-white border rounded-lg shadow-lg z-50"
                                    style={{ top: menuPosition.top + 40, left: menuPosition.left }}
                                >
                                    {togglebuttonsList.map((item, index) => (
                                        <Menu.Item key={index}>
                                            {({ active }) => (
                                                <button
                                                    className={`block w-full text-left px-4 py-2 ${active ? "bg-gray-100" : ""}`}
                                                    onClick={() => _handleMenuClick(item)}
                                                >
                                                    {item}
                                                </button>
                                            )}
                                        </Menu.Item>
                                    ))}
                                </Menu.Items>,
                                document.body
                            )}
                        </Menu>
                    )}
                </div>
            </div>

            <div className="mt-2 flex items-center space-x-3">
                <div className="text-gray-500">
                    <div className="flex items-center">
                        <span>ID: {id}</span>
                        <span className="mx-2">•</span>
                        <span>Last synced {lastSynced} ago</span>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default AccountCard;
