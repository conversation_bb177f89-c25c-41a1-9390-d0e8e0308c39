import React, { useState } from "react";
// import TableComponent from "./table";
import dynamic from "next/dynamic";

const TableComponent = dynamic(() => import("@/components/SimpleTable/table"), { ssr: false });
const DeploymentOverView = () => {
  const [deploymentStats] = useState({
    total: 10,
    successful: 8,
    failed: 2,
    pending: 8,
  });

  const [recentDeployments] = useState([
    { id: "D-001", name: "Project A", status: "SUCCESS", date: "2024-07-15" },
    { id: "D-002", name: "Project B", status: "SUCCESS", date: "2024-07-15" },
    { id: "D-003", name: "Project C", status: "FAILED", date: "2024-07-14" },
    { id: "D-004", name: "Project D", status: "FAILED", date: "2024-07-13" },
  ]);

  const [recentPipelineTriggers] = useState([
    { id: "T-001", name: "Pipeline X", status: "SUCCESS", date: "2024-07-15" },
    { id: "T-002", name: "Pipeline Y", status: "RUNNING", date: "2024-07-15" },
    { id: "T-003", name: "Pipeline Z", status: "FAILED", date: "2024-07-14" },
    { id: "T-004", name: "Pipeline ZX", status: "FAILED", date: "2024-07-13" },
  ]);

  const recentDeploymentHeaders = [
    { key: 'id', label: '#' },
    { key: 'name', label: 'Project Name' },
    { key: 'status', label: 'Status' },
    { key: 'date', label: 'Date' }
  ];

  const handleDeploymentRowClick = (deployment) => {


    window.alert(deployment);
  };

  const handlePipelineRowClick = (pipeline) => {

  };

  return (
    <>
      <div className="relative h-screen">
        <div className="w-full mb-6">
          <div className="mx-auto col-span-2 pl-6">
            <div className="text-lg text-black font-bold mt-4">
              Deployment & Pipeline
            </div>
            <h3 className="mt-3">Overview</h3>
          </div>
          <div className="px-6 py- bg-white">
            <div className="flex flex-wrap justify-between gap-4 px-4 pt-8 pb-0 lg:px-8">
              <DeploymentCard
                title="Total Deployments"
                value={deploymentStats.total}
                iconColor="bg-blue-100"
                iconStroke="text-blue-600"
                icon={
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"
                  />
                }
              />
              <DeploymentCard
                title="Successful"
                value={deploymentStats.successful}
                iconColor="bg-green-100"
                iconStroke="text-green-600"
                icon={
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                }
              />
              <DeploymentCard
                title="Failed"
                value={deploymentStats.failed}
                iconColor="bg-red-100"
                iconStroke="text-red-600"
                icon={
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                }
              />
              <DeploymentCard
                title="Pending"
                value={deploymentStats.pending}
                iconColor="bg-yellow-100"
                iconStroke="text-yellow-600"
                icon={
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                }
              />
            </div>
          </div>

          {/* Recent Deployments Table */}
          <div className="max-h-[60vh] overflow-auto custom-scrollbar">
            <div className="mt-8 px-6">
              <TableComponent
                title="Recent Deployments"
                headers={recentDeploymentHeaders}
                data={recentDeployments}
                onRowClick={handleDeploymentRowClick}
                sortableColumns={{
                  id: true,
                  name: true,
                  status: true,
                  date: true
                }}
                itemsPerPage={20}
              />
            </div>

            {/* Recent Pipeline Triggers Table */}
            <div className=" px-6" style={{ marginTop: "-50px" }}>
              <TableComponent
                title="Recent Pipeline Triggers"
                data={recentPipelineTriggers}
                headers={recentDeploymentHeaders}
                onRowClick={handlePipelineRowClick}
                sortableColumns={{
                  id: true,
                  name: true,
                  status: true,
                  date: true
                }}
                itemsPerPage={3}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

const DeploymentCard = ({ title, value, iconColor, iconStroke, icon }) => {
  return (
    <div className="flex-1 min-w-[200px] max-w-[300px]">
      <div className="flex w-full max-w-full flex-col break-words rounded-lg border border-gray-200 bg-white text-gray-600 shadow-sm hover:shadow-md transition-shadow duration-300">
        <div className="p-3">
          <div
            className={`absolute -mt-10 h-16 w-16 rounded-xl ${iconColor} text-center shadow-sm`}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className={`mt-4 h-8 w-8 mx-auto ${iconStroke}`}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
              strokeWidth="2"
            >
              {icon}
            </svg>
          </div>
          <div className="pt-1 text-right">
            <p className="text-sm font-medium text-gray-500 capitalize">
              {title}
            </p>
            <h4 className="text-2xl font-bold tracking-tight text-blue-600 xl:text-3xl">
              {value}
            </h4>
          </div>
        </div>
        <hr className="opacity-50" />
      </div>
    </div>
  );
};

export default DeploymentOverView;
