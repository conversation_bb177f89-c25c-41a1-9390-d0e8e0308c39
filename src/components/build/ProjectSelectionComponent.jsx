import { useState } from 'react';
import { ArrowUpRight, Briefcase, FolderPlus } from 'lucide-react';
import Cookies from 'js-cookie';
import {
  Sheet,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Sheet<PERSON>rigger,
} from "@/components/ui/sheet";
import AdvancedApplicationForm from './AdvancedApplicationForm';
import ImportProjectForm from './ImportProjectForm';

const projectOptions = [
  {
    id: 'create',
    title: 'Create Advanced Applications',
    description: 'Create responsive web applications with intuitive interfaces',
    sheetTitle: 'Create Advanced Applications',
    icon: Briefcase
  },
  {
    id: 'import',
    title: 'Import Existing Codebase',
    description: 'Create responsive web applications with intuitive interfaces',
    sheetTitle: 'Import Existing Assets',
    icon: FolderPlus
  }
];

const ProjectSelectionComponent = ({ handleComplexProjectSubmit, isComplexProjectSubmitting, isLight, setIsModalOpen }) => {
  const [createOpen, setCreateOpen] = useState(false);
  const [importOpen, setImportOpen] = useState(false);

  const handleSubmit = async (formData) => {
    const isLoggedIn = Cookies.get('idToken');
    if (!isLoggedIn) {
      setCreateOpen(false);
      setIsModalOpen(true);
      return;
    }
    await handleComplexProjectSubmit(formData);
  };

  return (
    <div className="flex mt-8 items-center justify-center gap-6 mx-auto">
      {projectOptions.map((option) => {
        const Icon = option.icon;
        const isCreate = option.id === "create";
        const open = isCreate ? createOpen : importOpen;
        const setOpen = isCreate ? setCreateOpen : setImportOpen;

        return (
          <Sheet key={option.id} open={open} onOpenChange={setOpen}>
            <SheetTrigger>
              <div
                className={`w-80 h-60 rounded-lg p-4 flex text-left items-start flex-col border ${isLight ? "border-gray-200 hover:border-orange-400" : "border-white/10"} hover:border-orange-400/50 hover:text-orange-400 ${isLight ? "text-gray-600" : "text-gray-500"} cursor-pointer ${isLight ? "bg-white hover:bg-gradient-to-b hover:from-orange-50 hover:via-transparent hover:to-transparent" : "bg-white/5 hover:bg-gradient-to-b hover:from-orange-400/10 hover:via-transparent hover:to-transparent"} transition-all duration-300`}
              >
                <Icon size={40} className={`${isLight ? "text-orange-500" : "text-orange-400"} mb-4`} />

                <h3 className={`${isLight ? "text-gray-800" : "text-white"} text-lg font-medium mb-4`}>
                  {option.title}
                </h3>

                <p className={`${isLight ? "text-gray-600" : "text-gray-400"} text-sm mb-auto`}>
                  {option.description}
                </p>

                <div className="flex justify-start mt-4">
                  <div>
                    <ArrowUpRight size={24} className={isLight ? "text-orange-500" : "text-orange-400"} />
                  </div>
                </div>
              </div>
            </SheetTrigger>
            <SheetContent theme={isLight ? "light" : "dark"}>
              <SheetHeader>
                <SheetTitle>
                  <Icon size={40} className={`${isLight ? "text-orange-500" : "text-orange-400"} mt-4`} />
                  <h1 className={`${isLight ? "text-gray-800" : "text-white"} text-2xl font-light mt-6`}>{option.sheetTitle}</h1>
                </SheetTitle>
              </SheetHeader>

              {isCreate ? (
                <AdvancedApplicationForm
                  handleProjectSubmit={handleSubmit}
                  submitting={isComplexProjectSubmitting}
                  isLight={isLight}
                />
              ) : (
                <ImportProjectForm isLight={isLight} />
              )}
            </SheetContent>
          </Sheet>
        );
      })}
    </div>
  );
};

export default ProjectSelectionComponent; 