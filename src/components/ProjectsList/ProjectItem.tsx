import React, { useState, useRef, useEffect } from 'react';
import { ArrowUpRight, MoreVertical, Edit, Trash2, Copy } from 'lucide-react';
import { formatListDate } from "@/utils/datetime";


interface ProjectItemProps {
  project: any;
  isSelected: boolean;
  onClick: () => void;
  onEditTitle?: (project: any) => void;
  onDeleteProject?: (project: any) => void;
  onCloneProject?: (project: any) => void;
  isSharedProject?: boolean;
}

const ProjectItem: React.FC<ProjectItemProps> = ({ project, 
  isSelected, 
  onClick, 
  onEditTitle,
  onDeleteProject,
  onCloneProject,
  isSharedProject = false}) => {
    
    const [menuOpen, setMenuOpen] = useState(false);
  const [menuPosition, setMenuPosition] = useState({ top: 0, left: 0 });
  const menuRef = useRef<HTMLDivElement>(null);
  const buttonRef = useRef<HTMLButtonElement>(null);

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node) &&
          buttonRef.current && !buttonRef.current.contains(event.target as Node)) {
        setMenuOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleMenuClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (buttonRef.current) {
      const rect = buttonRef.current.getBoundingClientRect();
      // Position to the left of the button, aligned with the top
      setMenuPosition({
        top: rect.top,
        left: Math.max(rect.left - 160, 10) // Menu width is 192px (w-48), subtract to place on left, with minimum margin
      });
    }
    
    setMenuOpen(!menuOpen);
  };

  const handleEditTitle = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onEditTitle) {
      onEditTitle(project);
    }
    setMenuOpen(false);
  };

  const handleDeleteProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onDeleteProject) {
      onDeleteProject(project);
    }
    setMenuOpen(false);
  };

  const handleCloneProject = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (onCloneProject) {
      onCloneProject(project);
    }
    setMenuOpen(false);
  };

  return (
<div
      className={`project-hover-container flex items-center justify-between px-4 py-3 border-b border-gray-200 cursor-pointer w-full relative${
        isSelected ? 'bg-orange-50' : ''
      } hover:bg-orange-50 group`}
      onClick={onClick}

    >
   <div className="flex flex-col pr-20 w-full">
      <div className="text-sm font-medium text-gray-900 truncate">
          {project.Title}
        </div>
        <div className="text-xs text-gray-500 mt-1">
          <span className="truncate">{project?.creator_name}, </span>
          <span className="whitespace-nowrap">{formatListDate(project?.created_at)}</span>
        </div>
      </div>
      
      {/* Fixed position icons */}
      <div className="absolute right-4 top-0 bottom-0 flex items-center gap-3 pointer-events-none">
        <div className="w-5 h-5 flex items-center justify-center pointer-events-auto">
          <ArrowUpRight 
            size={16} 
            className="text-gray-500 group-hover:text-orange-500"
          />
        </div>
        
        <div className="w-5 h-5 flex items-center justify-center pointer-events-auto" ref={menuRef}>
          <button 
            ref={buttonRef}
            onClick={handleMenuClick}
            className="w-5 h-5 rounded-full hover:bg-gray-100 flex items-center justify-center"
          >
            <MoreVertical size={16} className="text-gray-500" />
          </button>
          
          {menuOpen && (
            <div 
              ref={menuRef}
              className="fixed w-48 bg-white rounded-md shadow-lg z-50 py-1 border border-gray-200"
              style={{
                top: `${menuPosition.top}px`,
                left: `${menuPosition.left}px`
              }}
            >
              {!isSharedProject && (
                <button
                  onClick={handleEditTitle}
                  className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                >
                  <Edit size={14} className="mr-2" />
                  Edit Title
                </button>
              )}
                  <button
                onClick={handleCloneProject}
                className="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
              >
                <Copy size={14} className="mr-2" />
                Clone Project
              </button>
              {!isSharedProject && (
                <button
                  onClick={handleDeleteProject}
                  className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-gray-100"
                >
                  <Trash2 size={14} className="mr-2" />
                  Delete Project
                </button>
              )}
              
          
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

ProjectItem.displayName = 'ProjectItem';

export default ProjectItem;