import React, { useState, useEffect, useRef } from 'react';
import { GitBranch, Check, Loader2, Plus, ChevronDown, X, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from 'lucide-react';
import { DynamicButton } from '../UIComponents/Buttons/DynamicButton';
import { listAllBranches, createBranch } from "@/utils/repositoryAPI";

interface CreateBranchModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (branchName: string, sourceBranch: string) => Promise<void>;
  branches: any[];
  isLoading: boolean;
}

const CreateBranchModal: React.FC<CreateBranchModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  branches,
  isLoading
}) => {
  const [newBranchName, setNewBranchName] = useState('');
  const [sourceBranch, setSourceBranch] = useState('main');

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">Create New Branch</h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              New Branch Name
            </label>
            <input
              type="text"
              placeholder="feature/my-new-branch"
              value={newBranchName}
              onChange={(e) => setNewBranchName(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Source Branch
            </label>
            <select
              value={sourceBranch}
              onChange={(e) => setSourceBranch(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              {branches.map((branch: any) => (
                <option key={branch.name} value={branch.name}>
                  {branch.name}
                </option>
              ))}
            </select>
          </div>
        </div>

        <div className="mt-6 flex justify-end space-x-3">
          <DynamicButton
            variant="secondary"
            size="small"
            text="Cancel"
            onClick={onClose}
            disabled={isLoading}
          />
          <DynamicButton
            variant="primary"
            size="small"
            text="Create Branch"
            onClick={() => onSubmit(newBranchName, sourceBranch)}
            loading={isLoading}
            disabled={isLoading || !newBranchName.trim()}
          />
        </div>
      </div>
    </div>
  );
};

// Branch Selector Modal component with pagination
interface BranchSelectorModalProps {
  isOpen: boolean;
  onClose: () => void;
  branches: any[];
  currentBranch: string;
  onBranchSelect: (branchName: string) => Promise<void>;
  isLoading: boolean;
  onCreateBranchClick: () => void;
  pagination: {
    currentPage: number;
    totalPages: number;
    perPage: number;
    totalCount: number;
  };
  onPageChange: (page: number) => void;
}

const BranchSelectorModal: React.FC<BranchSelectorModalProps> = ({
  isOpen,
  onClose,
  branches,
  currentBranch,
  onBranchSelect,
  isLoading,
  onCreateBranchClick,
  pagination,
  onPageChange
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const [pageInput, setPageInput] = useState(pagination.currentPage.toString());
  const [pageInputError, setPageInputError] = useState(false);
  
  // Update page input when pagination changes
  useEffect(() => {
    setPageInput(pagination.currentPage.toString());
    setPageInputError(false);
  }, [pagination.currentPage]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(event.target as Node)) {
        onClose();
      }
    };
    
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  const handlePageInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value.replace(/[^0-9]/g, '');
    setPageInput(value);
    setPageInputError(false);
  };

  const handlePageInputSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    // Handle empty input
    if (!pageInput.trim()) {
      setPageInput(pagination.currentPage.toString());
      return;
    }
    
    const page = parseInt(pageInput, 10);
    
    // Validate page number
    if (page > 0 && page <= pagination.totalPages) {
      onPageChange(page);
    } else {
      setPageInputError(true);
      // Keep the invalid input to show the error state
      setTimeout(() => {
        setPageInput(pagination.currentPage.toString());
        setPageInputError(false);
      }, 1500);
    }
  };

  if (!isOpen) return null;

  // Determine if pagination should be shown
  const showPagination = !isLoading && pagination.totalCount > 0 && pagination.totalPages > 1;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div ref={modalRef} className="bg-white rounded-lg p-6 w-full max-w-md max-h-[80vh] flex flex-col">
        <div className="flex justify-between items-center mb-4">
          <h2 className="text-lg font-semibold">Select Branch</h2>
          <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
            <X className="w-5 h-5" />
          </button>
        </div>
        
        <div className="overflow-y-auto flex-grow">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="w-8 h-8 text-blue-500 animate-spin" />
            </div>
          ) : branches.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No branches found</p>
            </div>
          ) : (
            <ul className="space-y-1">
              {branches.map((branch) => (
                <li key={branch.name}>
                  <button
                    onClick={() => onBranchSelect(branch.name)}
                    className={`w-full text-left px-3 py-2 rounded-md flex items-center transition-colors ${
                      currentBranch === branch.name
                        ? 'bg-blue-50 text-blue-700'
                        : 'hover:bg-gray-50'
                    }`}
                    disabled={isLoading}
                  >
                    {currentBranch === branch.name ? (
                      <Check className="w-4 h-4 mr-2 flex-shrink-0" />
                    ) : (
                      <GitBranch className="w-4 h-4 mr-2 flex-shrink-0" />
                    )}
                    <span className="truncate">{branch.name}</span>
                  </button>
                </li>
              ))}
            </ul>
          )}
        </div>
        
        {showPagination && (
          <div className="flex flex-wrap justify-center items-center gap-2 mt-4 pt-4 border-t border-gray-200">
            <div className="flex items-center">
              <button
                onClick={() => onPageChange(1)}
                disabled={pagination.currentPage <= 1 || isLoading}
                className={`p-1 rounded-md ${
                  pagination.currentPage <= 1 || isLoading
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="First page"
              >
                <ChevronsLeft className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage <= 1 || isLoading}
                className={`p-1 rounded-md ${
                  pagination.currentPage <= 1 || isLoading
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Previous page"
              >
                <ChevronLeft className="w-4 h-4" />
              </button>
            </div>
            
            <form onSubmit={handlePageInputSubmit} className="flex items-center mx-1">
              <div className="relative">
                <input
                  type="text"
                  value={pageInput}
                  onChange={handlePageInputChange}
                  className={`w-12 h-8 text-center border rounded-md focus:outline-none ${
                    pageInputError 
                      ? 'border-red-500 focus:ring-1 focus:ring-red-500' 
                      : 'border-gray-300 focus:ring-1 focus:ring-blue-500'
                  }`}
                  aria-label="Page number"
                />
                {pageInputError && (
                  <div className="absolute -bottom-6 left-1/2 transform -translate-x-1/2 text-xs text-red-500 whitespace-nowrap">
                    Invalid page
                  </div>
                )}
              </div>
              <span className="mx-2 text-sm text-gray-600 whitespace-nowrap">
                of {pagination.totalPages}
              </span>
              <button
                type="submit"
                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded hover:bg-gray-200 disabled:bg-gray-50 disabled:text-gray-400"
                disabled={isLoading}
              >
                Go
              </button>
            </form>
            
            <div className="flex items-center">
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage >= pagination.totalPages || isLoading}
                className={`p-1 rounded-md ${
                  pagination.currentPage >= pagination.totalPages || isLoading
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Next page"
              >
                <ChevronRight className="w-4 h-4" />
              </button>
              <button
                onClick={() => onPageChange(pagination.totalPages)}
                disabled={pagination.currentPage >= pagination.totalPages || isLoading}
                className={`p-1 rounded-md ${
                  pagination.currentPage >= pagination.totalPages || isLoading
                    ? 'text-gray-300 cursor-not-allowed'
                    : 'text-gray-700 hover:bg-gray-100'
                }`}
                aria-label="Last page"
              >
                <ChevronsRight className="w-4 h-4" />
              </button>
            </div>
          </div>
        )}
        
        <div className="mt-4 pt-4 border-t border-gray-200">
          <DynamicButton
            variant="primary"
            size="small"
            text="Create New Branch"
            icon={Plus}
            onClick={onCreateBranchClick}
            disabled={isLoading}
            className="w-full"
          />
        </div>
      </div>
    </div>
  );
};

interface BranchSelectorProps {
  projectId: string | number;
  containerId: string | number;
  currentBranch: string;
  onUpdate: (branch: string) => Promise<void>;
  className?: string;
}

export const BranchSelector: React.FC<BranchSelectorProps> = ({
  projectId,
  containerId,
  currentBranch,
  onUpdate,
  className = ''
}) => {
  const [branches, setBranches] = useState<any[]>([]);
  const [selectedBranch, setSelectedBranch] = useState(currentBranch || null);
  const [isLoading, setIsLoading] = useState(false);
  const [isFetchingBranches, setIsFetchingBranches] = useState(false);
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [isBranchModalOpen, setIsBranchModalOpen] = useState(false);
  const [repoError, setRepoError] = useState(false);
  
  // Pagination state
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    perPage: 30,
    totalCount: 0
  });

  useEffect(() => {
    if (currentBranch) {
      setSelectedBranch(currentBranch);
    } else {
      // If no current branch is provided, fetch branches and select main
      // Don't automatically select a branch until we have fetched the branches
      fetchBranches(1);
    }
  }, [currentBranch]);

  // Add a separate effect to handle branch selection after branches are loaded
  useEffect(() => {
    // Only try to auto-select a branch if we have branches and no branch is currently selected
    if (branches.length > 0 && !selectedBranch && !isFetchingBranches) {
      // First try to find 'main' or 'master' branch
      const defaultBranch = branches.find(b => b.name === 'main') || 
                            branches.find(b => b.name === 'master');
      
      if (defaultBranch) {
        handleBranchSelect(defaultBranch.name);
      } else if (branches.length > 0) {
        // If no main/master, use the first branch
        handleBranchSelect(branches[0].name);
      }
    }
  }, [branches, selectedBranch, isFetchingBranches]);

  const fetchBranches = async (page = 1) => {
    setIsFetchingBranches(true);
    try {
      const response = await listAllBranches(projectId, containerId, page, pagination.perPage);
      if (response?.detail === "404: Repository not found") {
        setRepoError(true);
        return;
      }
      
      // Handle case where branches might be empty
      if (!response.branches || response.branches.length === 0) {
        setBranches([]);
        setRepoError(false);
        return;
      }
      
      setBranches(response.branches || []);
      
      // Handle potential missing pagination data
      let totalPages = response.pagination?.total_pages || 1;
      let currentPage = response.pagination?.current_page || 1;
      
      // Ensure currentPage doesn't exceed totalPages
      const safePage = Math.min(currentPage, totalPages);
      if (currentPage > totalPages) {
        totalPages = currentPage;
        currentPage = totalPages;
      }
      setPagination({
        currentPage: safePage,
        totalPages: totalPages,
        perPage: response.pagination?.per_page || 30,
        totalCount: response.total_count || 0
      });
      
      setRepoError(false);
    } catch (error) {
      console.error("Error fetching branches:", error);
      setRepoError(true);
      setBranches([]);
      setPagination({
        currentPage: 1,
        totalPages: 1,
        perPage: 30,
        totalCount: 0
      });
    } finally {
      setIsFetchingBranches(false);
    }
  };

  const handleOpenBranchModal = () => {
    setIsBranchModalOpen(true);
    fetchBranches(1);
  };

  const handlePageChange = (page: number) => {
    // Validate page number before fetch
    if (page < 1 || page > pagination.totalPages) {
      return;
    }
    fetchBranches(page);
  };

  // Add retry logic for branch selection
  const handleBranchSelect = async (branchName: string, retryCount = 0) => {
    if (!branchName || isLoading) return;
    
    setIsLoading(true);
    try {
      // Add a small delay to ensure API is ready
      await new Promise(resolve => setTimeout(resolve, 200));
      
      await onUpdate(branchName);
      setSelectedBranch(branchName);
      setIsBranchModalOpen(false);
    } catch (error) {
      console.error("Error selecting branch:", error);
      
      // Retry logic - maximum 3 retries with increasing delay
      if (retryCount < 3) {
        setIsLoading(false);
        // Wait longer for each retry
        const retryDelay = 1000 * (retryCount + 1);
        
        setTimeout(() => {
          handleBranchSelect(branchName, retryCount + 1);
        }, retryDelay);
        
        return;
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleCreateBranch = async (newBranchName: string, sourceBranch: string) => {
    setIsLoading(true);
    try {
      await createBranch(projectId, containerId, newBranchName, sourceBranch);
      // Refresh and go to first page to make sure the new branch is visible
      await fetchBranches(1);
      await handleBranchSelect(newBranchName);
      setIsCreateModalOpen(false);
    } catch (error) {
      
    } finally {
      setIsLoading(false);
    }
  };

  if (repoError) {
    return (
      <div className="flex items-center gap-2 px-3 py-1.5 text-sm font-medium text-gray-500 bg-gray-50 border border-gray-200 rounded-md">
        <GitBranch className="w-4 h-4" />
        <span>Repository not available</span>
      </div>
    );
  }

  return (
    <>
      <button 
        onClick={handleOpenBranchModal}
        className={`
          flex items-center justify-between gap-2 px-3 py-1.5 w-full
          text-sm font-medium text-gray-700 
          bg-white border border-gray-300 rounded-md hover:bg-gray-50
          ${isFetchingBranches ? 'border-blue-200' : ''}
          transition-colors duration-200
          ${className}
        `}
      >
        <span className='flex gap-2 items-center'>
          <GitBranch className={`
            w-4 h-4
            ${isFetchingBranches ? 'text-blue-400' : ''}
            transition-colors duration-200
          `} />
          <span className="truncate">{selectedBranch}</span>
        </span>
        <ChevronDown size={16} />
      </button>

      <BranchSelectorModal
        isOpen={isBranchModalOpen}
        onClose={() => setIsBranchModalOpen(false)}
        branches={branches}
        currentBranch={selectedBranch || ''}
        onBranchSelect={handleBranchSelect}
        isLoading={isFetchingBranches}
        onCreateBranchClick={() => {
          setIsBranchModalOpen(false);
          setIsCreateModalOpen(true);
        }}
        pagination={pagination}
        onPageChange={handlePageChange}
      />

      <CreateBranchModal
        isOpen={isCreateModalOpen}
        onClose={() => setIsCreateModalOpen(false)}
        onSubmit={handleCreateBranch}
        branches={branches}
        isLoading={isLoading}
      />
    </>
  );
};