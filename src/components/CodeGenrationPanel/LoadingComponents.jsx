import {
    FaCheckCircle,
    FaSpinner,
    FaTimes,
    FaCheck,
    FaRocket,
    FaServer,
    FaCogs,
    FaFlag
  } from 'react-icons/fa';

  export const LoadingStep = ({ label, status, icon: Icon }) => (
    <div className="flex items-center space-x-4 py-3">
      <div className={`
        rounded-full p-3 transition-all duration-300
        ${status === 'completed' ? 'bg-green-100' :
          status === 'loading' ? 'bg-orange-100' :
          'bg-gray-100'}
      `}>
        {status === 'completed' ? (
          <FaCheck className="h-5 w-5 text-green-600" />
        ) : status === 'loading' ? (
          <Icon className={`h-5 w-5 ${status === 'loading' ? 'text-orange-600 animate-pulse' : 'text-gray-400'}`} />
        ) : (
          <Icon className="h-5 w-5 text-gray-400" />
        )}
      </div>
      <div className="flex-1">
        <p className={`font-medium ${
          status === 'completed' ? 'text-green-600' :
          status === 'loading' ? 'text-orange-600' :
          'text-gray-500'
        }`}>
          {label}
        </p>
        <div className="mt-1 h-1 w-full rounded-full bg-gray-100">
          {(status === 'completed' || status === 'loading') && (
            <div
              className={`h-1 rounded-full transition-all duration-500 ${
                status === 'completed' ? 'bg-green-500 w-full' :
                'bg-orange-500 w-3/4 animate-pulse'
              }`}
            />
          )}
        </div>
      </div>
    </div>
  );

  export const SuccessState = ({ onComplete }) => (
    <div className="text-center py-6">
      <div className="mb-4 flex justify-center">
        <div className="rounded-full bg-green-100 p-4">
          <FaCheckCircle className="h-8 w-8 text-green-500" />
        </div>
      </div>
      <h3 className="text-xl font-semibold text-gray-900 mb-2">
        Setup Complete!
      </h3>
      <p className="text-gray-600 mb-6">
        Your lab environment is ready. Redirecting you in a moment...
      </p>
      <div className="flex justify-center space-x-3 items-center text-sm text-orange-600">
        <FaSpinner className="animate-spin" />
        <span>Preparing workspace...</span>
      </div>
    </div>
  );

  export const FullScreenLoader = ({ logginfo, onClose, isCompleted = false }) => {
    const steps = [
      { label: 'Installing Lab Resources', id: 'install', icon: FaRocket },
      { label: 'Provisioning Infrastructure', id: 'provision', icon: FaServer },
      { label: 'Configuring Lab Environment', id: 'config', icon: FaCogs },
      { label: 'Finalizing Setup', id: 'finalizing', icon: FaFlag }
    ];

    const getActiveSteps = (message) => {
      if (!message) return ['install'];
      return message.split('|').map(step => step.trim().toLowerCase());
    };

    const activeSteps = getActiveSteps(logginfo);

    const calculateProgress = () => {
      if (isCompleted) return 100;
      if (!activeSteps.length) return 20;
      return (activeSteps.length / steps.length) * 100;
    };

    const getStepStatus = (stepId) => {
      if (isCompleted) return 'completed';

      if (!logginfo) {
        return stepId === 'install' ? 'loading' : 'pending';
      }

      const stepIndex = steps.findIndex(step => step.id === stepId);
      const activeStepIndex = activeSteps.indexOf(stepId);

      if (activeStepIndex === -1) {
        const lastActiveIndex = steps.findIndex(step =>
          activeSteps.includes(step.id)
        );
        return stepIndex < lastActiveIndex ? 'completed' : 'pending';
      }

      if (activeStepIndex === activeSteps.length - 1) {
        return 'loading';
      }

      return 'completed';
    };

    return (
      <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div className="bg-white rounded-2xl shadow-2xl w-full max-w-2xl mx-4 relative overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-2 bg-gray-100">
            <div
              className={`h-full transition-all duration-500 ${
                isCompleted ? 'bg-green-500' : 'bg-orange-500'
              }`}
              style={{ width: isCompleted ? '100%' : `${calculateProgress()}%` }}
            />
          </div>

          {!isCompleted && (
            <button
              onClick={onClose}
              className="absolute top-4 right-4 p-2 rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Close"
            >
              <FaTimes className="h-5 w-5 text-gray-500" />
            </button>
          )}

          <div className="p-8 pt-12">
            {isCompleted ? (
              <SuccessState />
            ) : (
              <>
                <div className="text-center mb-8">
                  <h2 className="text-2xl font-bold text-gray-900">Preparing Your Lab</h2>
                  <p className="mt-2 text-gray-600">
                    Please wait while we set up your laboratory environment
                  </p>
                </div>

                <div className="space-y-6 mt-8">
                  {steps.map((step) => (
                    <LoadingStep
                      key={step.id}
                      label={step.label}
                      icon={step.icon}
                      status={getStepStatus(step.id)}
                    />
                  ))}
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    );
  };

  export const ConfirmationModal = ({ onConfirm, onCancel }) => (
    <div className="fixed inset-0 flex items-center justify-center z-50">
      <div className="fixed inset-0 bg-gray-900/50 backdrop-blur-sm" onClick={onCancel} />
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full mx-4 p-6 z-50 relative">
        <div className="text-center">
          <div className="mx-auto w-12 h-12 rounded-full bg-red-100 flex items-center justify-center mb-4">
            <FaTimes className="h-6 w-6 text-red-600" />
          </div>
          <h3 className="text-xl font-bold text-gray-900 mb-2">
            Stop Creation Process?
          </h3>
          <p className="text-gray-600 mb-6">
            Are you sure you want to stop the lab creation process? This action cannot be undone.
          </p>
        </div>
        <div className="flex space-x-3 justify-center">
          <button
            onClick={onCancel}
            className="px-6 py-2.5 text-sm font-medium text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors"
          >
            Cancel
          </button>
          <button
            onClick={onConfirm}
            className="px-6 py-2.5 text-sm font-medium text-white bg-red-600 hover:bg-red-700 rounded-lg transition-colors"
          >
            Stop Process
          </button>
        </div>
      </div>
    </div>
  );