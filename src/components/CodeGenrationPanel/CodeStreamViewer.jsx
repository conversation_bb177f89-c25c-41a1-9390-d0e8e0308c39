import React, { useEffect } from "react";
import { Fa<PERSON><PERSON><PERSON><PERSON><PERSON>, FaFolder } from "react-icons/fa";
import FileIcon from "../FileIcon";
import StreamingCodeContent from "../StreamingCodeContent";
import { useCodeGeneration } from "../Context/CodeGenerationContext";
import EmptyStateView from "@/components/Modal/EmptyStateModal"
import GitLineDiffViewer from "../GitDiffStreamingContent";

// Main component with display name
function CodeStreamViewer() {
  const {
      files, setFiles,
      selectedFile, setSelectedFile,
      latestStreamedFile, setLatestStreamedFile,
      diffHtml, setDiffHtml,
      openFolders, setOpenFolders,
      searchTerm, setSearchTerm,
      streamedUpdates, setStreamedUpdates,
      incommingFileUpdates,
  } = useCodeGeneration();

    // Helper to get file extension
  const getFileExtension = (filename) => {
      const ext = filename.split('.').pop();
      return ext === filename ? '' : ext;
    };
    
  const buildFileTree = (fileList) => {
    const fileTree = {};
    fileList.forEach((file) => {
      const parts = file.filepath.split("/");
      let current = fileTree;

      parts.forEach((part, index) => {
        if (index === parts.length - 1) {
          if (!current.files) current.files = [];
          current.files.push(file);
        } else {
          if (!current.folders) current.folders = {};
          if (!current.folders[part]) current.folders[part] = {};
          current = current.folders[part];
        }
      });
    });
    return fileTree;
  };

  const getDiffCount = (diff) => {
    if (!diff) return null;
    const addedLines = (diff.match(/^\+/gm) || []).length;
    const removedLines = (diff.match(/^\-/gm) || []).length;
    
    if (addedLines === 0 && removedLines === 0) return null;
    
    return (
      <div className="flex items-center space-x-1 text-xs font-medium">
        {addedLines > 0 && (
          <span className="text-green-600">+{addedLines-1}</span>
        )}
        {removedLines > 0 && (
          <span className="text-red-600">-{removedLines-1}</span>
        )}
      </div>
    );
  };

  const formatDiffContent = (file) => {
    if (!file.diff) return null;
  
    // Helper to check if string starts with diff header
    const hasDiffHeader = (str) => /^diff --git/.test(str);
    
    // Helper to check if string has unified diff header
    const hasUnifiedHeader = (str) => /^--- /.test(str.split('\n')[0]) && /^\+\+\+ /.test(str.split('\n')[1]);
    
    // Helper to check if it's just content changes without headers
    const isContentOnlyDiff = (str) => /^[-+@]/.test(str.trim());
  

  
    // Helper to create unified header if missing
    const createUnifiedHeader = (filename) => {
      return `--- a/${filename}\n+++ b/${filename}`;
    };
  
    // Helper to create hunk header if missing
    const createHunkHeader = () => {
      return '@@ -1,1 +1,1 @@';
    };
  
    let formattedDiff = file.diff.trim();
    const lines = formattedDiff.split('\n');
  
    // Case 1: If it's already a complete git diff, return as is
    if (hasDiffHeader(formattedDiff)) {
      return formattedDiff;
    }
  
    // Case 2: If it has unified header but no git diff header
    if (hasUnifiedHeader(formattedDiff)) {
      formattedDiff = `diff --git a/${file.filename} b/${file.filename}\n${formattedDiff}`;
      return formattedDiff;
    }
  
    // Case 3: If it's just content changes with hunk header
    if (isContentOnlyDiff(formattedDiff) && formattedDiff.includes('@@ ')) {
      formattedDiff = `diff --git a/${file.filename} b/${file.filename}\n${createUnifiedHeader(file.filename)}\n${formattedDiff}`;
      return formattedDiff;
    }
  
    // Case 4: If it's just content changes without any headers
    if (isContentOnlyDiff(formattedDiff)) {
      formattedDiff = `diff --git a/${file.filename} b/${file.filename}\n${createUnifiedHeader(file.filename)}\n${createHunkHeader()}\n${formattedDiff}`;
      return formattedDiff;
    }
  
    // Case 5: If it's a new file
    if (lines.every(line => line.startsWith('+'))) {
      formattedDiff = `diff --git a/${file.filename} b/${file.filename}\n--- /dev/null\n+++ b/${file.filename}\n@@ -0,0 +1,${lines.length} @@\n${formattedDiff}`;
      return formattedDiff;
    }
  
    // Case 6: If it's a deleted file
    if (lines.every(line => line.startsWith('-'))) {
      formattedDiff = `diff --git a/${file.filename} b/${file.filename}\n--- a/${file.filename}\n+++ /dev/null\n@@ -1,${lines.length} +0,0 @@\n${formattedDiff}`;
      return formattedDiff;
    }
  
    return formattedDiff;
  };
  

  const renderDiff = (file) => {
    if (!file) return;
  
    if (
      selectedFile?.filepath === file.filepath && 
      diffHtml && 
      !file.diff && 
      diffHtml.type === StreamingCodeContent &&
      file.current_code === selectedFile.current_code
    ) {
      return;
    }
  
    if (!file.diff || file.diff.trim() === "") {
      setDiffHtml(
        <StreamingCodeContent 
          content={file.current_code || ''} 
          language={getFileExtension(file.filename) || 'yaml'}
        />
      );
      return;
    }
  
    try {
      // Format the diff content
      const formattedDiff = formatDiffContent(file);
      if (!formattedDiff) {
        throw new Error('Invalid diff content');
      }
      
      setDiffHtml(
        <GitLineDiffViewer diff={formattedDiff} />
      )
    } catch (error) {
      setDiffHtml(
        <StreamingCodeContent 
          content={file.current_code || ''} 
          language={getFileExtension(file.filename) || 'yaml'}
        />
      );
    }
  };

  useEffect(() => {
    if (incommingFileUpdates){
      const fileData = incommingFileUpdates;
      setFiles((prevFiles) => {
        const fileIndex = prevFiles.findIndex(
          (file) => file.filepath === fileData.filepath
        );
        
        const updatedFiles = [...prevFiles];
        if (fileIndex !== -1) {
          updatedFiles[fileIndex] = { ...updatedFiles[fileIndex], ...fileData };
        } else {
          updatedFiles.push(fileData);
        }

        setStreamedUpdates(prev => ({
          ...prev,
          [fileData.filepath]: fileData
        }));

        if (!selectedFile || (selectedFile && selectedFile.filepath === fileData.filepath)) {
          setLatestStreamedFile(fileData);
          renderDiff(fileData);
        }

        return updatedFiles;
      });
    };
  }, [incommingFileUpdates,selectedFile]);


  useEffect(() => {
    if (selectedFile && streamedUpdates[selectedFile.filepath]) {
      const updatedFile = {
        ...selectedFile,
        ...streamedUpdates[selectedFile.filepath]
      };
  
      // Check if selectedFile is different from updatedFile
      if (JSON.stringify(selectedFile) !== JSON.stringify(updatedFile)) {
        setSelectedFile(updatedFile);
        renderDiff(updatedFile);
      }
    }
  }, [streamedUpdates]);
  

  const handleFileClick = (file) => {
    if (selectedFile && file.filepath === selectedFile.filepath) {
      setSelectedFile(null);
      const latestVersion = streamedUpdates[file.filepath] || latestStreamedFile;
      if (latestVersion) {
        renderDiff(latestVersion);
      }
    } else {
      setSelectedFile(file);
      renderDiff(file);
    }
  };

  const handleFolderClick = (folderPath) => {
    setOpenFolders((prevOpenFolders) => ({
      ...prevOpenFolders,
      [folderPath]: !prevOpenFolders[folderPath],
    }));
  };

  const renderFileTree = (tree, folderPath = "") => {
    const folderEntries = Object.entries(tree.folders || {});
    const fileEntries = tree.files || [];

    const filteredFileEntries = fileEntries.filter(file =>
      file.filename.toLowerCase().includes(searchTerm.toLowerCase())
    );

 

    return (
      <>
        {folderEntries.map(([folderName, folderContent]) => {
          const fullPath = `${folderPath}/${folderName}`;
          const isOpen = openFolders[fullPath] !== false;

          return (
            <li key={fullPath} className="mb-1">
              <div
                className="flex items-center cursor-pointer hover:bg-gray-100 rounded-md p-1.5 transition-colors"
                onClick={() => handleFolderClick(fullPath)}
              >
                <span className="text-gray-500 mr-2">
                  {isOpen ? <FaFolderOpen className="text-blue-500" /> : <FaFolder className="text-gray-400" />}
                </span>
                <span className="text-sm font-medium text-gray-700">{folderName}</span>
              </div>
              {isOpen && (
                <ul className="ml-4 border-l border-gray-200">
                  {renderFileTree(folderContent, fullPath)}
                </ul>
              )}
            </li>
          );
        })}
        {filteredFileEntries.map((file) => (
          <li
            key={file.filepath}
            className={`group relative flex items-center px-2 py-1.5 my-0.5 rounded-md cursor-pointer transition-all
              ${selectedFile?.filepath === file.filepath
                ? "bg-blue-100 text-blue-700"
                : "hover:bg-gray-100"
              }`}
            onClick={() => handleFileClick(file)}
          >
            <div className="flex items-center w-full">
              <FileIcon filename={file.filename} />
              <span className="text-sm truncate flex-1">{file.filename}</span>
              <div className="ml-2">
                {getDiffCount(file.diff)}
              </div>
              {selectedFile?.filepath === file.filepath && 
               streamedUpdates[file.filepath] && (
                <div className="ml-2 text-xs text-blue-500">•</div>
              )}
            </div>
          </li>
        ))}
      </>
    );
  };

  return (
    <>
    {files.length === 0 ? (
      <div className="text-center flex justify-center h-96 items-center">
        <EmptyStateView type="noFilesToDisplay" />
      </div>
    ):(
    <div className="h-[calc(76vh)] flex bg-gray-50">
      
      <div className="w-64 flex flex-col border-r border-gray-200 bg-white">
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <input
              type="text"
              placeholder="Search files..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full px-4 py-2 pl-10 bg-gray-100 border-0 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:bg-white transition-all"
            />
          </div>
        </div>
        
        <div className="flex-1 overflow-auto px-3 py-4">
          <ul className="space-y-1">
            {renderFileTree(buildFileTree(files))}
          </ul>
        </div>
      </div>

      <div className="flex-1 flex flex-col min-w-0">
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 bg-white">
          <div className="flex items-center">
            <h1 className="text-lg font-semibold text-gray-900">
              {selectedFile ? (
                <>
                  {selectedFile.filename}
                  <span className="ml-2 text-sm text-gray-500">(Selected)</span>
                  {streamedUpdates[selectedFile.filepath] && (
                    <span className="ml-2 text-xs text-blue-500">(Updates available)</span>
                  )}
                </>
              ) : latestStreamedFile ? (
                <>
                  {latestStreamedFile.filename}
                  <span className="ml-2 text-sm text-gray-500">(Latest Change)</span>
                </>
              ):""}
            </h1>
            {(selectedFile || latestStreamedFile) && (
              <span className="ml-4 px-2.5 py-0.5 bg-blue-100 text-blue-700 text-sm rounded-full">
                {(selectedFile || latestStreamedFile).extension}
              </span>
            )}
          </div>
        </div>

        <div className="">
              {diffHtml}
        </div>
      </div>
    </div>
    )}
    </>
  );
}

CodeStreamViewer.displayName = 'CodeStreamViewer';

export default CodeStreamViewer;