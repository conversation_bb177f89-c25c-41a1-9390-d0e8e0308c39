import React, { useState, useRef, useEffect } from 'react';
import ReactDOM from 'react-dom';
import { Plus, ArrowUp, X, FileText, Loader2 } from 'lucide-react';
import { uploadMultipleAttachments } from '@/utils/fileAPI';
import { useParams, useSearchParams } from 'next/navigation';
import { BootstrapTooltip } from '../UIComponents/ToolTip/Tooltip-material-ui';
import { FaStopCircle } from 'react-icons/fa';
// Custom hook for detecting clicks outside of an element
const useClickOutside = (ref, handler, exceptRef = null) => {
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (
        ref.current &&
        !ref.current.contains(event.target) &&
        !(exceptRef && exceptRef.current && exceptRef.current.contains(event.target))
      ) {
        handler();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [ref, handler, exceptRef]);
};

// File Attachment Popup component using Portal
const FileAttachmentPopup = ({
  files,
  onRemoveFile,
  onClearFiles,
  onAddMoreFiles,
  onClose,
  triggerButtonRef,
  isUploading
}) => {
  const popupRef = useRef(null);
  const firstButtonRef = useRef(null);

  // Handle click outside
  useClickOutside(popupRef, onClose, triggerButtonRef);

  // Focus management
  useEffect(() => {
    // Store the previously focused element
    const previousFocus = document.activeElement;

    // Focus the first interactive element in the popup
    if (firstButtonRef.current) {
      firstButtonRef.current.focus();
    }

    // Cleanup function to restore focus when component unmounts
    return () => {
      if (triggerButtonRef.current) {
        triggerButtonRef.current.focus();
      } else if (previousFocus) {
        previousFocus.focus();
      }
    };
  }, []);

  if (files.length === 0) return null;

  // Find the position of the trigger button to position the popup
  const calculatePosition = () => {
    if (!triggerButtonRef.current) return { top: '60px', left: '20px' };

    const rect = triggerButtonRef.current.getBoundingClientRect();
    return {
      bottom: `${window.innerHeight - rect.top + 10}px`,
      left: `${rect.left}px`
    };
  };

  const position = calculatePosition();

  return ReactDOM.createPortal(
    <div
      className="fixed inset-0 z-[9999] pointer-events-none"
      aria-modal="true"
      role="dialog"
      aria-label="Attached files"
    >
      <div
        ref={popupRef}
        className="absolute pointer-events-auto bg-white rounded-lg shadow-2xl border border-gray-300"
        style={{
          bottom: position.bottom,
          left: position.left,
          width: '300px',
          zIndex: 9999
        }}
        tabIndex={-1}
      >
        <div className="flex justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
          <h3 className="text-gray-800 font-semibold">Attached Files</h3>
          <div className="flex gap-3">
            <button
              ref={firstButtonRef}
              onClick={onAddMoreFiles}
              className="text-orange-500 hover:text-orange-700 text-sm font-medium"
              disabled={isUploading}
            >
              + Add
            </button>
            <button
              onClick={onClearFiles}
              className="text-gray-600 hover:text-gray-800 text-sm font-medium"
              disabled={isUploading}
            >
              Clear
            </button>
          </div>
        </div>
        <div className="max-h-64 overflow-y-auto">
          {files.map((file, index) => (
            <div key={index} className="flex items-center justify-between p-3 hover:bg-gray-50 border-b border-gray-100">
              <div className="flex items-center gap-2 overflow-hidden">
                <FileText size={16} className="text-gray-600 flex-shrink-0" />
                <div className="flex flex-col overflow-hidden">
                  <span className="text-sm text-gray-700 truncate">
                    {file.name}
                  </span>

                  {/* Show status indicators */}
                  {file.uploading && (
                    <div className="flex items-center gap-1 text-xs text-gray-500">
                      <Loader2 size={10} className="text-orange-500 animate-spin" />
                      <span>Uploading...</span>
                    </div>
                  )}

                  {file.uploaded && (
                    <span className="text-xs text-green-600">Uploaded</span>
                  )}

                  {file.error && (
                    <span className="text-xs text-red-600">{file.error}</span>
                  )}
                </div>
              </div>
              <button
                onClick={() => onRemoveFile(file)}
                className="text-gray-400 hover:text-gray-600 flex-shrink-0"
                aria-label={`Remove file ${file.name}`}
                disabled={file.uploading}
              >
                <X size={16} />
              </button>
            </div>
          ))}
        </div>
      </div>
    </div>,
    document.body
  );
};

// Main ChatInput component
const ChatInput = ({
  isStopped,
  inputValue,
  setInputValue,
  handleSendMessage,
  isReady,
  textAreaRef,
  activeReplyTo,
  isAiTyping,
  // New props for model selection
  wsConnection
}) => {
  const [attachedFiles, setAttachedFiles] = useState([]);
  const [showAttachments, setShowAttachments] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadedAttachments, setUploadedAttachments] = useState([]);
  const [isMultiline, setIsMultiline] = useState(false);
  const searchParams = useSearchParams();
  const[isStop,setIsStop]=useState(false)
  const fileInputRef = useRef(null);
  const plusButtonRef = useRef(null);

  // Get project ID from URL
  const { projectId } = useParams();

  // Handle file selection
  const handleFileSelect = async (e) => {
    if (e.target.files.length > 0) {
      const newFiles = [...attachedFiles];
      const filesToUpload = [];

      // First, add files to state with uploading status
      for (let i = 0; i < e.target.files.length; i++) {
        const currentFile = e.target.files[i];

        // Check if file already exists in array (by name and size)
        const isDuplicate = newFiles.some(
          existingFile =>
            existingFile.name === currentFile.name &&
            existingFile.size === currentFile.size
        );

        if (!isDuplicate) {
          // Add file with uploading status
          const fileObj = {
            name: currentFile.name,
            size: currentFile.size,
            uploaded: false,
            uploading: true, // New flag to indicate upload in progress
            file: currentFile
          };

          newFiles.push(fileObj);
          filesToUpload.push(fileObj);
        }
      }

      // Update state to show files with loading state
      setAttachedFiles(newFiles);
      setShowAttachments(true);

      // Start upload immediately if we have projectId
      if (projectId && filesToUpload.length > 0) {
        setIsUploading(true);

        try {
          // Extract actual File objects to upload
          const files = filesToUpload.map(fileObj => fileObj.file);

          // Upload all files
          const results = await uploadMultipleAttachments(files, projectId);

          // Update the attached files with uploaded status
          setAttachedFiles(prev => {
            const updatedFiles = [...prev];

            results.forEach((result, index) => {
              if (result.success !== false) {
                const fileIndex = updatedFiles.findIndex(
                  f => f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploaded = true;
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].attachmentId = result.attachment_id;
                }
              } else {
                // Handle upload failure
                const fileIndex = updatedFiles.findIndex(
                  f => f.name === files[index].name && f.size === files[index].size
                );

                if (fileIndex !== -1) {
                  updatedFiles[fileIndex].uploading = false;
                  updatedFiles[fileIndex].error = result.error || "Upload failed";
                }
              }
            });

            return updatedFiles;
          });

          // Add to uploadedAttachments state for message sending
          const newUploadedAttachments = results
            .filter(result => result.success !== false)
            .map(result => ({
              attachment_id: result.attachment_id,
              filename: result.filename,
              file_location: result.file_location,
              size: result.size
            }));

          setUploadedAttachments(prev => [...prev, ...newUploadedAttachments]);
        } catch (error) {


          // Mark all files as failed
          setAttachedFiles(prev => {
            return prev.map(f => {
              if (f.uploading) {
                return { ...f, uploading: false, error: "Upload failed" };
              }
              return f;
            });
          });
        } finally {
          setIsUploading(false);
        }
      }
    }
  };

  // Handle removing a file
  const handleRemoveFile = (fileToRemove) => {
    // Don't allow removal of files that are still uploading
    if (fileToRemove.uploading) {
      return;
    }

    // Remove from attached files
    const updatedFiles = attachedFiles.filter(file => file !== fileToRemove);
    setAttachedFiles(updatedFiles);

    // Remove from uploaded attachments if already uploaded
    if (fileToRemove.attachmentId) {
      setUploadedAttachments(prev => prev.filter(
        attachment => attachment.attachment_id !== fileToRemove.attachmentId
      ));
    }

    // Close popup if no files left
    if (updatedFiles.length === 0) {
      setShowAttachments(false);
    }
  };

  // Handle clearing all files
  const handleClearFiles = () => {
    setAttachedFiles([]);
    setUploadedAttachments([]);
    setShowAttachments(false);
  };

  // Handle the Plus button click to open file browser or toggle popup
  const handlePlusClick = () => {
    if (attachedFiles.length > 0) {
      setShowAttachments(!showAttachments);
    } else {
      // Force trigger file input click with small delay to improve reliability
      setTimeout(() => {
        if (fileInputRef.current) {
          fileInputRef.current.click();
        }
      }, 10);
    }
  };

  // Handle Add More Files button in popup
  const handleAddMoreFiles = () => {
    setTimeout(() => {
      if (fileInputRef.current) {
        fileInputRef.current.click();
      }
    }, 10);
  };

  // Close popup
  const handleClosePopup = () => {
    setShowAttachments(false);
  };

  // Upload files before sending message
  const uploadFiles = async () => {
    if (!projectId || attachedFiles.length === 0) return [];

    // Filter for files that haven't been uploaded yet
    const filesToUpload = attachedFiles.filter(file => !file.uploaded);
    if (filesToUpload.length === 0) return uploadedAttachments;

    setIsUploading(true);

    try {
      // Extract actual File objects to upload
      const files = filesToUpload.map(fileObj => fileObj.file);

      // Upload all files
      const results = await uploadMultipleAttachments(files, projectId);

      // Update the attached files with uploaded status
      const updatedFiles = [...attachedFiles];
      results.forEach((result, index) => {
        if (result.success !== false) {
          const fileIndex = updatedFiles.findIndex(
            f => f.name === files[index].name && f.size === files[index].size
          );

          if (fileIndex !== -1) {
            updatedFiles[fileIndex].uploaded = true;
            updatedFiles[fileIndex].attachmentId = result.attachment_id;
          }
        }
      });

      setAttachedFiles(updatedFiles);

      // Filter successful uploads and add to uploadedAttachments
      const newUploadedAttachments = results
        .filter(result => result.success !== false)
        .map(result => ({
          attachment_id: result.attachment_id,
          filename: result.filename,
          file_location: result.file_location,
          size: result.size
        }));

      setUploadedAttachments(prev => [...prev, ...newUploadedAttachments]);
      return newUploadedAttachments;
    } catch (error) {

      return [];
    } finally {
      setIsUploading(false);
    }
  };

  // Completely rewritten to eliminate focus issues
  const adjustTextAreaHeight = (element) => {
    if (!element) return;

    // First check if empty (we'll handle this specially)
    if (!element.value.trim()) {
      // Reset to single line and update state
      element.style.height = '36px';
      if (isMultiline) setIsMultiline(false);
      return;
    }

    // Save scroll and selection positions
    const scrollPosition = element.scrollTop;
    const selectionStart = element.selectionStart;
    const selectionEnd = element.selectionEnd;

    // Temporarily reset height to get proper scrollHeight measurement
    element.style.height = '36px';

    // Get content height and calculate if multiline needed
    const contentHeight = element.scrollHeight;
    const minHeight = 36; // Single line height
    const maxHeight = 140; // 4 lines max

    // Set height based on content (capped at max)
    const newHeight = Math.min(Math.max(contentHeight, minHeight), maxHeight);
    element.style.height = `${newHeight}px`;

    // Set overflow if needed
    element.style.overflowY = contentHeight > maxHeight ? 'auto' : 'hidden';

    // Only update state if mode needs to change
    // Use a larger buffer to prevent flicker at transition point
    const shouldBeMultiline = contentHeight > (minHeight + 14);
    if (isMultiline !== shouldBeMultiline) {
      setIsMultiline(shouldBeMultiline);
    }

    // Restore selection and scroll position
    element.selectionStart = selectionStart;
    element.selectionEnd = selectionEnd;
    element.scrollTop = scrollPosition;
  };



  // Check if send button should be enabled
  const isSendEnabled = (inputValue.trim().length > 0 || attachedFiles.length > 0) && !isUploading;

  // Modified handleSendClick function to include file attachments
  const handleSendClick = async () => {
    if (!isSendEnabled) return;

    try {
      // First, upload any files that haven't been uploaded yet
      let finalAttachments = [...uploadedAttachments];

      if (attachedFiles.some(file => !file.uploaded)) {
        const newAttachments = await uploadFiles();
        finalAttachments = [...uploadedAttachments, ...newAttachments];
      }

      // If we have files and a WebSocket connection, send them with the message
      if (finalAttachments.length > 0 && wsConnection?.readyState === WebSocket.OPEN) {
        const attachmentIds = finalAttachments.map(a => a.attachment_id);

        wsConnection.send(
          JSON.stringify({
            type: 'send_message',
            content: inputValue.trim(),
            parent_id: activeReplyTo,
            attachment_ids: attachmentIds,
            attachments: finalAttachments,
          })
        );

        // Clear the files after sending
        setAttachedFiles([]);
        setUploadedAttachments([]);
        setInputValue('');
        setIsMultiline(false);

        // Reset textarea height
        if (textAreaRef.current) {
          textAreaRef.current.value = '';
          textAreaRef.current.style.height = '36px';
          textAreaRef.current.style.overflowY = 'hidden';
          textAreaRef.current.focus();
        }

        // If replying to a message, clear the reply state
        if (activeReplyTo) {
          // This should be implemented in parent component
        }
      } else {
        // If no files or no WebSocket, just call the regular handleSendMessage
        handleSendMessage();

        // Clear the files after sending
        setAttachedFiles([]);
        setUploadedAttachments([]);
        setIsMultiline(false);
      }
    } catch (error) {

    }
  };

  // Focus textarea when component mounts
  useEffect(() => {
    if (textAreaRef.current) {
      textAreaRef.current.style.height = '36px';
      textAreaRef.current.focus();
    }

    // Cleanup
    return () => {
      // Any cleanup (if needed)
    };
  }, []);

  useEffect(()=>{
    if(isAiTyping==false){
      setIsStop(false)
    }
  },[isAiTyping])
  return (
    <div className="w-full max-w-lg mx-auto rounded-xl bg-white shadow-[0px_2px_4px_-2px_rgba(0,0,0,0.05),0px_-1px_5px_0px_rgba(0,0,0,0.05)] outline outline-1 outline-offset-[-1px] outline-[#f26a1b]/50 overflow-hidden">
        <style dangerouslySetInnerHTML={{__html: `
          textarea.chat-input {
            caret-color: rgba(107, 114, 128, 0.5);
            caret-width: thin;
          }
          textarea.chat-input::selection {
            background-color: rgba(59, 130, 246, 0.2);
          }
        `}} />
        <div className="p-1.5 relative">
          {/* Main container */}
          <div className="flex flex-col">
            {/* Textarea only */}
            <div className={`w-full ${isMultiline ? 'mb-8' : ''}`}>
              <textarea
                ref={textAreaRef}
                value={inputValue}
                placeholder={isReady
                  ? (isAiTyping 
                    ? isStop
                    ? "Stopping response...":"Generating your response, please wait..." 
                    : "Reply to message...")
                  : "Reply to message..."}
                className="chat-input w-full resize-none border-none bg-transparent text-gray-700 placeholder-gray-400  font-medium font-['Hind'] leading-tight focus:outline-none focus:ring-0"
                style={{
                  height: '43px',
                  maxHeight: '150px',
                  overflowY: 'hidden',
                  minHeight: '43px',
                  paddingRight: '40px',  // Space for send button
                  paddingLeft: '32px',   // Adjusted padding to align with attachment button
                  paddingTop: '15px',     // For vertical centering
                }}
                rows={1}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    if (isSendEnabled) {
                      handleSendClick();
                    }
                  }
                }}
                onChange={(e) => {
                  setInputValue(e.target.value);
                  adjustTextAreaHeight(e.target);
                }}
                disabled={isUploading||isStopped}
              />
            </div>

            {/* Buttons - absolutely positioned */}
            <div className={`absolute ${isMultiline ? 'bottom-1.5' : 'top-1/2 -translate-y-1/2'} left-1.5`}>
              <button
                ref={plusButtonRef}
                onClick={handlePlusClick}
                className={`size-6 bg-white rounded-full outline outline-1 outline-offset-[-1px] outline-gray-200 flex items-center justify-center hover:bg-gray-50 relative ${
                  isUploading ? 'opacity-50 cursor-not-allowed' : ''
                }`}
                aria-label={attachedFiles.length > 0 ? "Show attached files" : "Add files"}
                aria-haspopup="dialog"
                aria-expanded={showAttachments}
                disabled={isUploading}
              >
                {isUploading ? (
                  <Loader2 size={16} className="text-gray-600 animate-spin" />
                ) : (
                  <Plus size={16} className="text-gray-600" />
                )}

                {/* Badge showing number of attached files */}
                {attachedFiles.length > 0 && (
                  <div className="absolute -top-1.5 -right-1.5 flex items-center justify-center min-w-[14px] h-[14px] text-[9px] font-semibold text-white bg-orange-500 rounded-full px-0.5">
                    {attachedFiles.length > 99 ? '99+' : attachedFiles.length}
                  </div>
                )}
              </button>
            </div>

            {/* Send button - absolutely positioned */}
            <div className={`absolute ${isMultiline ? 'bottom-1.5 right-1.5' : 'top-1/2 -translate-y-1/2 right-1.5'}`}>
              <BootstrapTooltip 
                title={
                  isUploading 
                    ? "Uploading files..." 
                    : isAiTyping 
                      ? (isStop ? "Stopping response..." : "Stop response")
                      : "Send message"
                } 
                placement="top"
              >
                <button
                  onClick={isAiTyping ? () => {
                    setIsStop(true)
                    if (wsConnection?.readyState === WebSocket.OPEN) {
                      wsConnection.send(
                        JSON.stringify({
                          type: "stop_streaming",
                          task_id: searchParams.get("task_id"),
                        })
                      );
                    }
                  } : handleSendClick}
                  disabled={isAiTyping ? !isReady || isStop: (!isSendEnabled || !isReady)}
                  className={`size-6 rounded-full flex items-center justify-center ${
                    isAiTyping 
                      ?
                       isStop?
                       "bg-gray-200 text-gray-400 cursor-not-allowed"
                       :"bg-red-500 text-white hover:bg-red-600"
                      : isSendEnabled && isReady 
                        ? "bg-orange-500 text-white hover:bg-orange-600"
                        : "bg-gray-200 text-gray-400 cursor-not-allowed"
                  }`}
                  aria-label={isAiTyping ? isStop?"Stopping response...":"Stop response" : "Send message"}
                >
                  {isUploading ? (
                    <Loader2 size={16} className="animate-spin" />
                  ) : isAiTyping ? (
                    <FaStopCircle size={20} className="" />
                  ) : (
                    <ArrowUp size={16} />
                  )}
                </button>
              </BootstrapTooltip>
            </div>
          </div>

          {/* Hidden file input */}
          <input
            type="file"
            ref={fileInputRef}
            className="hidden"
            multiple
            onChange={handleFileSelect}
            aria-hidden="true"
            disabled={isUploading}
            accept=".pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt,.md,.json,.csv,.ppt,.pptx"
            onClick={(e) => {
              // Reset the input value to ensure change event fires even if selecting the same file
              e.currentTarget.value = '';
            }}
          />

          {/* File attachment popup with portal */}
          {showAttachments && attachedFiles.length > 0 && (
            <FileAttachmentPopup
              files={attachedFiles}
              onRemoveFile={handleRemoveFile}
              onClearFiles={handleClearFiles}
              onAddMoreFiles={handleAddMoreFiles}
              onClose={handleClosePopup}
              triggerButtonRef={plusButtonRef}
              isUploading={isUploading}
            />
          )}
        </div>
      </div>
  );
};

export default ChatInput;
