import React, { useState, useRef, useLayoutEffect, useEffect, memo, useMemo, useCallback } from 'react';
import { FaCheckCircle } from 'react-icons/fa';
import { ImSpinner2 } from "react-icons/im";

// SVG Icons for custom components
const RevertIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="10" viewBox="0 0 12 10" fill="none">
    <path id="revert" d="M2.47591 9.57499V8.59999H7.93348C8.55373 8.59999 9.07416 8.3728 9.49479 7.91843C9.91554 7.46418 10.1259 6.91974 10.1259 6.28511C10.1259 5.65049 9.91554 5.10718 9.49479 4.65518C9.07416 4.2033 8.55373 3.97736 7.93348 3.97736H2.78023L4.66085 5.85818L3.96429 6.55474L0.899414 3.48986L3.96429 0.424988L4.66085 1.12155L2.78023 3.00236H7.93348C8.8181 3.00236 9.56716 3.32474 10.1807 3.96949C10.7942 4.61424 11.1009 5.38611 11.1009 6.28511C11.1009 7.18411 10.7942 7.95718 10.1807 8.6043C9.56716 9.25143 8.8181 9.57499 7.93348 9.57499H2.47591Z" fill="#F26A1B"/>
  </svg>
);

// Operation Item component
const OperationItem = memo(({ id, status, operation, label, path, onPathClick }) => {
  const handlePathClick = (e) => {
    e.stopPropagation();
    if (onPathClick) {
      onPathClick(path, id);
    }
  };

  // Determine if operation is in progress based on both status and operation type
  const isInProgress = () => {
    if (status === 'completed' || status === 'Completed') return false;
    return true; // All non-completed operations are considered in progress
  };

  return (
    <div className="self-stretch inline-flex justify-start items-center gap-1.5">
      <div className="flex-shrink-0 w-[14.62px] h-[14.62px] flex justify-center items-center">
        {!isInProgress() ? (
          <FaCheckCircle size={14} className="text-[#0E9F6E]" />
        ) : (
          <ImSpinner2 size={14} className="text-[#f26a1b] animate-spin" />
        )}
      </div>
      <div className="px-1.5 bg-opacity-5 rounded-lg flex justify-start items-start gap-1">
        <div className="text-[#1f2a37] text-xs font-medium font-['Hind'] leading-tight">
          {label || status}:
        </div>
        <div
          className="text-[#1f2a37] text-xs font-normal font-['Hind'] underline leading-tight cursor-pointer hover:text-[#f26a1b]"
          onClick={handlePathClick}
        >
          {path}
        </div>
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Only re-render if critical props changed
  return (
    prevProps.id === nextProps.id &&
    prevProps.status === nextProps.status &&
    prevProps.path === nextProps.path &&
    prevProps.label === nextProps.label
  );
});

// Main component
const CodebaseOperations = memo(({ title, operations, isCollapsed: propIsCollapsed, onCollapseToggle, allowInteractionDuringStreaming = true }) => {
  const [internalIsCollapsed, setInternalIsCollapsed] = useState(true);
  const [isMinimized, setIsMinimized] = useState(false);
  const scrollContainerRef = useRef(null);
  const prevOperationsLengthRef = useRef(operations.length);
  const prevScrollHeightRef = useRef(0);
  const prevScrollTopRef = useRef(0);
  const operationsMapRef = useRef(new Map());
  const operationsIdsRef = useRef([]);
  const operationsRef = useRef(operations);
  const sortedOperationsRef = useRef([]);
  const prevIsCollapsedRef = useRef(propIsCollapsed !== undefined ? propIsCollapsed : internalIsCollapsed);
  const prevIsMinimizedRef = useRef(isMinimized);

  // Use either controlled or uncontrolled collapsing state
  const isCollapsed = propIsCollapsed !== undefined ? propIsCollapsed : internalIsCollapsed;

  // Sort operations with memoization to prevent unnecessary re-calculations
  const sortedOperations = useMemo(() => {
    try {
      // Quick equality check to prevent unnecessary recomputation
      if (operations === operationsRef.current && operations.length === operationsRef.current.length) {
        // Only do deep equality check if lengths match
        let allEqual = true;
        for (let i = 0; i < operations.length; i++) {
          if (operations[i].id !== operationsRef.current[i].id ||
              operations[i].status !== operationsRef.current[i].status) {
            allEqual = false;
            break;
          }
        }
        if (allEqual && sortedOperationsRef.current && sortedOperationsRef.current.length > 0) {
          return sortedOperationsRef.current; // Return previous result if nothing changed
        }
      }

      // Update reference for future comparisons
      operationsRef.current = [...operations];

      // Create sorted operations
      const newSortedOperations = [...operations].sort((a, b) => {
        const isCompletedA = a.status === 'completed' || a.status === 'Completed';
        const isCompletedB = b.status === 'completed' || b.status === 'Completed';

        if (isCompletedA && !isCompletedB) return -1;
        if (!isCompletedA && isCompletedB) return 1;

        // For operations with the same completion status, preserve their order by ID
        // This is critical for scroll position stability
        const indexA = operationsIdsRef.current.indexOf(a.id);
        const indexB = operationsIdsRef.current.indexOf(b.id);

        if (indexA !== -1 && indexB !== -1) {
          return indexA - indexB; // Preserve existing order
        }

        return 0;
      });

      // Store the result in a ref for future comparisons
      sortedOperationsRef.current = newSortedOperations;

      return newSortedOperations;
    } catch (error) {

      return operations; // Fallback to unsorted operations on error
    }
  }, [operations]);

  // Find the latest operation (last item after sorting)
  const latestOperation = useMemo(() => {
    try {
      return sortedOperations && sortedOperations.length > 0 ?
        sortedOperations[sortedOperations.length - 1] : null;
    } catch (error) {

      return null;
    }
  }, [sortedOperations]);

  // Get all operations except the latest
  const previousOperations = useMemo(() => {
    try {
      return latestOperation && sortedOperations ?
        sortedOperations.slice(0, -1) : [];
    } catch (error) {

      return [];
    }
  }, [latestOperation, sortedOperations]);

  // Keep track of operation IDs to detect changes and maintain stable ordering
  useLayoutEffect(() => {
    try {
      // Skip if operations haven't changed
      if (operations === operationsRef.current) return;

      // Capture the IDs of existing operations to maintain stable ordering
      if (operations.length > 0) {
        const currentIds = operations.map(op => op.id);

        // Preserve order of existing IDs and add new ones at the end
        const existingIds = [...operationsIdsRef.current];
        const newIds = [];

        // Keep existing IDs in their current order
        const remainingIds = existingIds.filter(id => currentIds.includes(id));

        // Identify new IDs that weren't in the existing list
        currentIds.forEach(id => {
          if (!existingIds.includes(id)) {
            newIds.push(id);
          }
        });

        // Update the ref with preserved order plus new IDs
        operationsIdsRef.current = [...remainingIds, ...newIds];
      }

      // Update the operations map
      const newOperationsMap = new Map();
      operations.forEach(op => {
        newOperationsMap.set(op.id, op);
      });
      operationsMapRef.current = newOperationsMap;
    } catch (error) {

    }
  }, [operations]);

  // Improved scroll preservation logic with better position tracking
  useLayoutEffect(() => {
    try {
      // Don't run if nothing has changed
      if (operations === operationsRef.current &&
          isCollapsed === prevIsCollapsedRef.current &&
          isMinimized === prevIsMinimizedRef.current) {
        return;
      }

      // Update refs for next comparison
      prevIsCollapsedRef.current = isCollapsed;
      prevIsMinimizedRef.current = isMinimized;

      // Only run if container is visible (not collapsed, not minimized)
      if (!isCollapsed && !isMinimized && scrollContainerRef.current) {
        const container = scrollContainerRef.current;

        // Immediately capture current scroll state to avoid race conditions
        const currentScrollTop = container.scrollTop;
        const currentScrollHeight = container.scrollHeight;
        const currentClientHeight = container.clientHeight;

        // Calculate if user was at the bottom
        const isAtBottom = (currentScrollTop + currentClientHeight + 5) >= currentScrollHeight;

        // Detect if content was scrolled at all
        const isScrolled = currentScrollTop > 0;

        // Give DOM a chance to update before fixing scroll
        requestAnimationFrame(() => {
          // Only after DOM has updated
          if (container) {
            if (isAtBottom) {
              // If at bottom, stay at bottom
              container.scrollTop = container.scrollHeight;
            } else if (isScrolled) {
              // If scrolled to a specific position, try to maintain that position
              // even if content changed above or below

              // First measure any content change
              const heightDelta = container.scrollHeight - currentScrollHeight;

              // Don't modify scroll if heights are the same
              if (heightDelta !== 0) {
                // Adjust scroll position based on content changes, but don't go negative
                const newScrollTop = Math.max(0, currentScrollTop + heightDelta);

                // Set scroll with animation disabled (instant scrolling)
                container.style.scrollBehavior = 'auto';
                container.scrollTop = newScrollTop;
                // Restore default behavior after scroll is set
                setTimeout(() => {
                  container.style.scrollBehavior = '';
                }, 0);
              } else {
                // Even if no height change, ensure scroll position is preserved exactly
                container.scrollTop = currentScrollTop;
              }
            }

            // Update refs for next render
            prevScrollHeightRef.current = container.scrollHeight;
            prevScrollTopRef.current = container.scrollTop;
            prevOperationsLengthRef.current = operations.length;
          }
        });
      }
    } catch (error) {

    }
  }, [operations, isCollapsed, isMinimized]);

  // Also preserve scroll when toggling minimize
  useEffect(() => {
    try {
      if (!isCollapsed && !isMinimized && scrollContainerRef.current) {
        // Use a more conservative approach
        requestAnimationFrame(() => {
          if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollTop = prevScrollTopRef.current;
          }
        });
      }
    } catch (error) {

    }
  }, [isMinimized, isCollapsed]);

  // Before the DOM updates, record scrollHeight and scrollTop
  const handleScroll = useCallback(() => {
    if (scrollContainerRef.current) {
      prevScrollHeightRef.current = scrollContainerRef.current.scrollHeight;
      prevScrollTopRef.current = scrollContainerRef.current.scrollTop;
    }
  }, []);

  // Toggle accordion function
  const toggleCollapse = useCallback(() => {
    if (onCollapseToggle) {
      onCollapseToggle(!isCollapsed);
    } else {
      setInternalIsCollapsed(!internalIsCollapsed);
    }
  }, [isCollapsed, internalIsCollapsed, onCollapseToggle]);

  const handleRevert = useCallback((e) => {
    e.preventDefault();
    e.stopPropagation();
    // Revert functionality is not implemented
  }, []);

  const handlePathClick = useCallback((path, id) => {
    // Path click handler is not implemented
  }, []);

  // Determine if operations include any in-progress tasks
  const hasInProgressOperations = useMemo(() =>
    operations.some(op => op.status !== 'completed' && op.status !== 'Completed')
  , [operations]);

  // Toggle component minimize/maximize
  const toggleMinimize = useCallback((e) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    setIsMinimized(prev => !prev);
  }, []);

  return (
    <div className={`w-full bg-white rounded-[5px] shadow-[0px_1px_2px_0px_rgba(0,0,0,0.08)] outline outline-1 outline-offset-[-1px] outline-[#fad4b4] flex flex-col mb-4 transition-all`}>
      {/* Header - Always visible */}
      <div
        className="self-stretch pl-3 pr-1.5 py-[5px] bg-[#fdf9f6] border-b border-black/5 flex justify-between items-center cursor-pointer w-full text-left"
        onClick={toggleCollapse}
      >
        <div className="flex-1 flex justify-start items-center gap-[5px]">
          <div className="text-[#1f2a37] text-[13px] font-semibold font-['Hind'] leading-snug">
            {title}
          </div>
          {hasInProgressOperations && allowInteractionDuringStreaming && !isCollapsed && !isMinimized && (
            <div className="text-[#f26a1b] text-[11px] font-medium">
              (Updating in real-time)
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          {/* Minimize/Maximize button */}
          <button
            className="w-5 h-5 flex items-center justify-center"
            onClick={toggleMinimize}
            type="button"
            aria-label={isMinimized ? "Maximize" : "Minimize"}
          >
            <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
              {isMinimized ? (
                // Maximize icon (down chevron)
                <path d="M2 4L6 8L10 4" stroke="#4B5563" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              ) : (
                // Minimize icon (up chevron)
                <path d="M2 8L6 4L10 8" stroke="#4B5563" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
              )}
            </svg>
          </button>


          {/* Revert button */}
          <button
            className="w-6 h-6 p-1 bg-[#fbe4d1] rounded-sm flex justify-center items-center hover:bg-[#f9d6ba] transition-colors"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleRevert(e);
            }}
            type="button"
            aria-label="Revert"
          >
            <RevertIcon />
          </button>
        </div>
      </div>

      {/* Content area */}
      <div className="self-stretch flex flex-col justify-start items-start">
        {/* Previous operations - only visible when not collapsed and not minimized */}
        {!isCollapsed && !isMinimized && previousOperations.length > 0 && (
          <div
            ref={scrollContainerRef}
            className="self-stretch px-3 pt-1 pb-0 flex flex-col justify-start items-start gap-1 max-h-[140px] overflow-y-auto custom-scrollbar"
            onClick={e => e.stopPropagation()}
            onScroll={handleScroll}
            style={{
              scrollBehavior: 'auto',
              overscrollBehavior: 'contain',
              WebkitOverflowScrolling: 'touch',
              willChange: 'scroll-position',
              contain: 'content',
              paddingRight: '5px'
            }}
          >
            {/* Display all previous operations */}
            {previousOperations.map((op) => (
              <div key={op.id} className="operation-item w-full mb-1">
                <OperationItem
                  id={op.id}
                  status={op.status}
                  operation={op.operation || ''}
                  label={op.label || op.status}
                  path={op.path}
                  onPathClick={handlePathClick}
                />
              </div>
            ))}
          </div>
        )}

        {/* Latest operation - ALWAYS visible regardless of collapsed or minimized state */}
        {latestOperation && (
          <div className={`self-stretch px-3 py-2 ${!isCollapsed && !isMinimized && previousOperations.length > 0 ? 'border-t border-black/5' : ''}`} onClick={e => e.stopPropagation()}>
            <OperationItem
              id={latestOperation.id}
              status={latestOperation.status}
              operation={latestOperation.operation || ''}
              label={latestOperation.label || latestOperation.status}
              path={latestOperation.path}
              onPathClick={handlePathClick}
            />
          </div>
        )}
      </div>
    </div>
  );
}, (prevProps, nextProps) => {
  // Prevent re-rendering when props remain the same
  if (prevProps.isCollapsed !== nextProps.isCollapsed ||
      prevProps.title !== nextProps.title ||
      prevProps.allowInteractionDuringStreaming !== nextProps.allowInteractionDuringStreaming) {
    return false; // Props changed, should re-render
  }

  // More complex comparison for operations
  if (prevProps.operations.length !== nextProps.operations.length) {
    return false; // Different length, should re-render
  }

  // Check if any operations have changed status (frequently changing property)
  for (let i = 0; i < prevProps.operations.length; i++) {
    const prevOp = prevProps.operations[i];
    const nextOp = nextProps.operations[i];

    if (!nextOp || prevOp.id !== nextOp.id || prevOp.status !== nextOp.status) {
      return false; // Operation changed, should re-render
    }
  }

  return true; // No changes, prevent re-render
});

// Helper function to create properly formatted operation objects
export const createOperation = (id, status, path, label) => {
  return {
    id,
    status,
    path,
    label: label || status
  };
};

export default CodebaseOperations;

// Example usage:
/*
export const CodebaseOperationsExample = () => {
  const sampleOperations = [
    { id: 'op1', status: 'Completed', label: 'Completed', path: 'src/routes/PatternShowcase.css' },
    { id: 'op2', status: 'Completed', label: 'Completed', path: 'src/routes/ProductCard.js' },
    { id: 'op3', status: 'edit', label: 'edit', path: 'src/routes/HomePage.js' },
    { id: 'op4', status: 'edit', label: 'edit', path: 'src/routes/HomePage.css' },
    { id: 'op5', status: 'edit', label: 'edit', path: 'src/routes/ProductDetailsPage.js' }
  ];

  return (
    <CodebaseOperations
      title="Completed codebase changes"
      operations={sampleOperations}
    />
  );
};
*/