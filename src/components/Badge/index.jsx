import React from "react";

const badgeColors = {
    default: "border-transparent bg-primary text-white",
    destructive: "bg-red-600 border-transparent text-white",
    success: "bg-green-600 border-transparent text-white",
    info: "bg-orange-500 border-transparent text-white",
    warning: "bg-yellow-600 border-transparent text-white",
    secondary: "bg-gray-600 border-transparent text-white",
    dark: "bg-black border-transparent text-white",
};

const badgeVariants = {
  outline: "border border-current bg-transparent",
  soft: "bg-opacity-100 ",
};

function Badge({ className = "", color = "default", variant = "outline", ...props }) {
  const colorClasses = badgeColors[color] || badgeColors.default;
  const variantClasses = badgeVariants[variant] || badgeVariants.outline;

  return (
    <div
      className={`inline-flex items-center rounded-sm px-2.5 py-0.5 text-xs font-semibold  ${colorClasses} ${variantClasses} ${className}`}
      {...props}
    />
  );
}

export default Badge;
