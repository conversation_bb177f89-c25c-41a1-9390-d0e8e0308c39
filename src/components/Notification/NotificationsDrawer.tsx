import { Bell } from "lucide-react"
import { PanelProvider } from "../Context/PanelContext";
import NotificationList from "../Notification/NotificationList";
import Drawer from "../Drawer";

import '@/styles/notificationlist.css';

interface NotificationsDrawerProps {
    isOpen: boolean;
    onClose: () => void;
    onClearAll: () => void;
    handleValueChange: (value: number) => void;
}

export const NotificationsDrawer: React.FC<NotificationsDrawerProps> = ({
    isOpen,
    onClose,
    onClearAll,
    handleValueChange,
}) => {
    // Use ThemeContext and fallback to prop if provided


    return (
        <PanelProvider>
            <Drawer
                isOpen={isOpen}
                onClose={onClose}
                placement="left"
                showBackdrop={false}
                title={
                    <span className="drawer-title">
                        <div className="drawer-title-container">
                        <Bell className="drawer-title-icon" />
                            <span className="drawer-title-name">Notifications</span>
                        </div>
                    </span>
                }
                width={400}
            >
                <NotificationList
                    handleValueChange={handleValueChange}
                    handleDrawerToggle={onClose}
                    handleClearAll={onClearAll}
          
                />
            </Drawer>
        </PanelProvider>
    );
};