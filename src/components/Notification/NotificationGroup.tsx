import { cn } from "@/lib/utils";
import { CheckCircle, Trash2 } from "lucide-react";
import { formatUTCToLocal } from "@/utils/datetime";
import { IconButton } from "../UIComponents/Buttons/IconButton";

interface NotificationGroupProps {
  date: string;
  items: any[];
  notificationTooltips: { [key: number]: string };
  markRead: (id: any, handler: (value: number) => void) => void;
  handleValueChange: (value: number) => void;
  handleDrawerToggle: (drawerName: string) => void;
  openDeleteModal: (notificationId: string, receiverId: string) => void;
  onNotificationClick: (item: any) => void;
}

const handleTypeDisplay = (item: any) => {
  if (item.type === "code_generation") {
    if (item.data.message.includes("code-generation-job")) {
      return "Code Generation";
    } else if (item.data.message.includes("code-maintenance-job")) {
      return "Code Maintenance";
    } else {
      return item.type;
    }
  }
  return item.type;
};

export const NotificationGroup = ({
  date,
  items,
  notificationTooltips,
  markRead,
  handleValueChange,
  handleDrawerToggle,
  openDeleteModal,
  onNotificationClick,
}: NotificationGroupProps) => {
  return (
    // <div className="notification-group">
    //   <div className="notification-date-header">{date}</div>
    //   {items.map((item: any) => (
    //     <div
    //       key={item.notification_id}
    //       className={cn("notification-item group", {
    //         "notification-item-unread": !item.is_read,
    //       })}
    //       title={notificationTooltips[item.data.project_id]}
    //     >
    //       <div className="notification-content-wrapper">
    //         <IconButton
    //           className="notification-bell-container group"
    //           icon={
    //             <>
    //               <Bell
    //                 className={cn(
    //                   "notification-bell-icon",
    //                   item.is_read
    //                     ? "notification-bell-icon-read"
    //                     : "notification-bell-icon-unread"
    //                 )}
    //               />
    //               {!item.is_read && <span className="notification-unread-indicator" />}
    //             </>
    //           }
    //           tooltip={(!item.is_read ? "Mark as read" : "")}
    //           onClick={() => markRead(item.notification_id, handleValueChange)}
    //         />
    //         <div className="notification-content">
    //           <div className="notification-type">{handleTypeDisplay(item)}</div>
    //           <div
    //             className="notification-message"
    //             onClick={() => onNotificationClick(item)}
    //           >
    //             {item.data.message}
    //           </div>
    //           <div className="notification-timestamp-container">
    //             <span className="notification-timestamp">
    //               {formatUTCToLocal(item.created_at)}
    //             </span>
    //           </div>
    //         </div>
    //         <div className="relative inline-block">
    // <UiButton
    //   onClick={() => openDeleteModal(item.notification_id, item.receiver_id)}
    //   icon={Trash2}
    //   variant="icon"
    //   tooltip="Delete notification"
    //   className="notification-delete-button"
    //   iconStyle="notification-delete-icon"
    // />
    //         </div>
    //       </div>
    //     </div>
    //   ))}
    // </div>

    // <div className="mb-6 last:mb-0">
    //   <div className="text-xs font-medium uppercase text-gray-500  mb-3 px-2">
    //     {date}
    //   </div>

    //   <div className="space-y-2">
    //     {items.map((item) => (
    //       <div
    //         key={item.notification_id}
    //         className={cn(
    //           "flex items-start p-3 rounded-lg transition-colors",
    //           "hover:bg-gray-50",
    //           !item.is_read && "bg-blue-50/50"
    //         )}
    //       >
    //         <div className="flex-1 min-w-0">
    //           <div className="flex items-center gap-2 mb-1">
    //             <span className="inline-flex items-center px-2 py-1 text-xs font-medium rounded-full bg-primary-100 text-primary-800">
    //               {handleTypeDisplay(item)}
    //             </span>
    //             {!item.is_read && (
    //               <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
    //             )}
    //           </div>

    //           <div
    //             onClick={() => onNotificationClick(item)}
    //             className="text-sm font-medium text-gray-900 cursor-pointer hover:underline"
    //           >
    //             {item.data.message}
    //           </div>

    //           <div className="flex items-center gap-3 mt-1">
    //             <span className="text-xs text-gray-500">
    //               {formatUTCToLocal(item.created_at)}
    //             </span>
    //             {item.data.task_id && (
    //               <span className="text-xs text-gray-400">
    //                 Task ID: {item.data.task_id.split('-').slice(0, 3).join('-')}...
    //               </span>
    //             )}
    //           </div>
    //         </div>

    //         <div className="flex items-center gap-2 ml-3">
    //           <IconButton
    //             icon={<Trash2 />}
    //             // size="sm"
    //             variant="ghost"
    //             onClick={() => openDeleteModal(item.notification_id, item.receiver_id)}
    //             className="opacity-0 group-hover:opacity-100 transition-opacity"
    //             // ariaLabel="Delete notification"
    //             tooltip="Delete notification"
    //           />

    //           {!item.is_read && (
    //             <IconButton
    //               icon={<CheckCircle />}
    //               // size="sm"
    //               variant="ghost"
    //               onClick={() => markRead(item.notification_id, handleValueChange)}
    //               tooltip={(!item.is_read ? "Mark as read" : "")}
    //             />
    //           )}
    //         </div>
    //       </div>
    //     ))}
    //   </div>
    // </div>
    <div className="notification-group">
      <div className="notification-date-header">{date}</div>

      <div className="space-y-2">
        {items.map((item) => (
          <div
            key={item.notification_id}
            className={cn(
              "bg-white rounded-lg shadow-sm border border-gray-200 p-4 cursor-pointer hover:border-blue-500 transition-all duration-200",
              "hover:bg-gray-50 transition-colors",
              !item.is_read && "bg-blue-50/50 border-blue-100"
            )}
          >
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <span className="px-2 py-1 rounded-md text-xs bg-primary-100 text-primary-800">
                  {handleTypeDisplay(item)}
                </span>
                {!item.is_read && (
                  <span className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                )}
              </div>
            </div>

            <h3
              onClick={() => onNotificationClick(item)}
              className="text-sm font-medium text-gray-900 cursor-pointer hover:underline mb-2"
            >
              {item.data.message}
            </h3>

            <div className="flex justify-between items-center pt-2 border-t border-gray-100">
              <span className="text-xs text-gray-500">
                {formatUTCToLocal(item.created_at)}
              </span>
              <div className="flex items-center gap-2">
                {!item.is_read && (
                  <IconButton
                    icon={<CheckCircle className="w-4 h-4" />}
                    variant="ghost"
                    onClick={() =>
                      markRead(item.notification_id, handleValueChange)
                    }
                    tooltip="Mark as read"
                    className="text-gray-400 hover:text-blue-500"
                  />
                )}
                <IconButton
                  icon={<Trash2 className="w-4 h-4" />}
                  variant="ghost"
                  onClick={() =>
                    openDeleteModal(item.notification_id, item.receiver_id)
                  }
                  tooltip="Delete notification"
                  className="text-gray-400 hover:text-red-500"
                />
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};
