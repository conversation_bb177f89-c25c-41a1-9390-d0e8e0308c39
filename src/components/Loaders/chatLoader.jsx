import React from "react";

const Chat<PERSON>oader = ({message}) => {
  return (
    <div className="relative flex flex-col space-y-6 p-4 w-full">
      {/* Centered Message with blur */}
      {message && (
        <div
          className="absolute top-0 left-0 right-0 bottom-0 flex items-center justify-center"
          style={{
            backdropFilter: "blur(10px)",
            zIndex: 10,
          }}
        >
          <p className="text-center text-gray-500 text-lg font-medium">
            {message}
          </p>
        </div>
      )}

      {/* Original skeleton loaders */}
      {[...Array(4)].map((_, index) => (
        <div key={index} className="flex items-start w-full">
          <div className="flex flex-col space-y-2 w-full">
            <div className="bg-gray-100 rounded-lg p-4 w-full">
              <div
                className="h-4 bg-gray-200 rounded animate-pulse mb-2"
                style={{
                  width: `${Math.floor(Math.random() * (95 - 70 + 1) + 70)}%`,
                }}
              />
              <div
                className="h-4 bg-gray-200 rounded animate-pulse mb-2"
                style={{
                  width: `${Math.floor(Math.random() * (95 - 70 + 1) + 70)}%`,
                }}
              />
              <div
                className="h-4 bg-gray-200 rounded animate-pulse mb-2"
                style={{
                  width: `${Math.floor(Math.random() * (95 - 70 + 1) + 70)}%`,
                }}
              />
            </div>
          </div>
        </div>
      ))}
    </div>
  );
};

export default ChatLoader;