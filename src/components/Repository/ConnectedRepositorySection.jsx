import React, { useState, useEffect, useContext } from 'react';
import { Loader2, Check ,RefreshCw } from 'lucide-react';
import RepositoryListTable from '../SimpleTable/RepoListTable';
import { fetchRepository,getkginfo, clone_and_build } from '@/utils/gitAPI';
import { usePathname } from 'next/navigation';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import { ProjectAssetContext } from "@/components/Context/ProjectAssetContext";
import {TableLoadingSkeleton} from "@/components/UIComponents/Loaders/LoaderGroup"
import { useWebSocket } from '../Context/WebsocketContext';

function ConnectedRepositorySection({ onClose ,onImport,repositoriesVal,setRepositories,activeTabVal, encryptedSCMId=null, organizationName=null, updateBuildStatuses}) {
  const { connectToSession } = useWebSocket();
  const [repositories, setRepositoriesVal] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isImporting, setIsImporting] = useState(false);
  const pathname = usePathname();
  const projectId = pathname.split("/")[2];
  const { showAlert } = useContext(AlertContext);
  const { updateSessionId ,setActiveTab ,activeTab , repositoryDetails} = useContext(ProjectAssetContext); 

  useEffect(() => {
    handleScanRepositories();
  }, [activeTabVal]);

  const handleScanRepositories = async () => {
    try {
      setIsLoading(true);
      if (activeTabVal === 'organization') {
        setRepositoriesVal(repositoryDetails);
      } else {
        const response = await fetchRepository();
        setRepositoriesVal(response);
      }
    } catch (error) {
      setError('Failed to fetch repositories');
      
      showAlert('Error fetching repositories. Please try again.', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleRepository = (index) => {
    const newRepositories = repositories.map((repo, i) =>
      i === index ? { ...repo, selected: !repo.selected } : repo
    );
    setRepositoriesVal(newRepositories);
  };

  const handleImportRepositories = async () => {
    const selectedRepos = repositories.filter(repo => repo.selected);
    
      
    if (selectedRepos.length === 0) {
      showAlert("Please select at least one repository.", "warning");
      return;
    }
  
    try {
      setIsImporting(true);
      onImport(selectedRepos);
      
      const payload = {
        project_id: projectId,
        encrypted_scm_id: encryptedSCMId,
        repositories: selectedRepos.map(repo => {
          const fullRepoName = repo.path ? repo.path.split('/').slice(-2).join('/') : repo.name;
  
          return {
            repo_id: String(repo.id),
            repo_name: fullRepoName,
            branch_name: repo.branch,
            repo_type: "private",
            associated: true
          };
        })
      };
  
      const response = await clone_and_build(payload);
      
      if (response.status === "success") {
        if (response?.buildIds) {
          updateBuildStatuses(response.buildIds, "clone");
        }

        const sessionId = response?.build_session_id;
        if (sessionId) {
          connectToSession(sessionId);
          updateSessionId(sessionId);
          showAlert('Git Public URL imported successfully!', 'success');
          setActiveTab('code');
        }
        await onImport();
      } else {
        showAlert("Failed to import repositories.", "error");
      }
    } catch (error) {
      
      showAlert('Error importing repositories. Please try again.', 'error');
    } finally {
      setIsImporting(false);
      onClose();
    }
  };

  function processRepositories(response, selectedRepositories) {
   
  
    // Map the repository details
    const listRepos = response.details.map((repo) => {
      const firstBranch = repo.branches[0];
      const firstBranchStatus = firstBranch?.builds?.kg_creation_status;
  
      return {
        id: repo.git_url.split("/").pop().split(".")[0],
        name: repo.git_url.split("/").pop().split(".")[0],
        gitUrl: repo.git_url.replace(".git", ""),
        repoType: repo.repo_type,
        branches: repo.branches.map((branch) => ({
          name: branch.name,
          buildId: branch.builds?.build_id,
          kg_creation_status: branch.builds?.kg_creation_status,
          status:
            branch.builds?.kg_creation_status === 0
              ? "Not Started"
              : branch.builds?.kg_creation_status === 1
                ? "In Progress"
                : branch.builds?.kg_creation_status === 2
                  ? "Completed"
                  : "Failed",
          lastUpdated: branch.builds?.last_updated,
          type:
            branch.name === "main" || branch.name === "master"
              ? "main"
              : branch.name.startsWith("feature/")
                ? "feature"
                : branch.name.startsWith("fix/")
                  ? "fix"
                  : branch.name.startsWith("dev")
                    ? "development"
                    : "other",
        })),
        selectedBranch: firstBranch?.name || "main",
        status:
          firstBranchStatus === 0
            ? "Not Started"
            : firstBranchStatus === 1
              ? "In Progress"
              : firstBranchStatus === 2
                ? "Completed"
                : "Failed",
        created_at: response.created_at,
      };
    });
  
 
  
    // Extract build IDs for selected repositories
    const buildIds = selectedRepositories.map((repoId) => {
 
  
      const repo = listRepos.find((r) => r.id === repoId.name);
  
    
  
      if (!repo) {
        throw new Error(`Repository with ID ${repoId.name} not found.`);
      }
  
      if (!repo.selectedBranch) {
        throw new Error(`No branch selected for repository ${repo.name}.`);
      }
  
      const selectedBranchObj = repo.branches.find(
        (b) => b.name === repo.selectedBranch
      );
  
      if (!selectedBranchObj) {
        throw new Error(
          `Selected branch '${repo.selectedBranch}' not found for repository ${repo.name}.`
        );
      }
  
      return selectedBranchObj.buildId;
    });
  
    return {  buildIds };
  }
  const handleBranchChange = (index, updates) => {
    setRepositoriesVal(prevRepos => {
      return prevRepos.map((repo, i) => {
        if (repo.name === updates.name) {
          return {
            ...repo,
            branch: updates.branch,
            clone_url: updates.clone_url,
            description: updates.description,
            name: updates.name
          };
        }
        return repo;
      });
    });
    
    
  };

  const fetchRepositoryData = async () => {
      try {
        const response = await getkginfo(projectId, true);
  
        if (response.details && response.details.length > 0) {
          const repoData = response.details.map((repo) => {
            const firstBranch = repo.branches[0];
            const firstBranchStatus = firstBranch?.builds?.kg_creation_status;
  
            return {
              id: repo.git_url.split("/").pop().split(".")[0],
              name: repo.git_url.split("/").pop().split(".")[0],
              gitUrl: repo.git_url.replace(".git", ""),
              repoType: repo.repo_type,
              branches: repo.branches.map((branch) => ({
                name: branch.name,
                buildId: branch.builds.build_id,
                kg_creation_status: branch.builds.kg_creation_status,
                status:
                  branch.builds.kg_creation_status === 0
                    ? "Not Started"
                    : branch.builds.kg_creation_status === 1
                    ? "In Progress"
                    : branch.builds.kg_creation_status === 2
                    ? "Completed"
                    : "Failed",
                lastUpdated: branch.builds.last_updated,
                type:
                  branch.name === "main" || branch.name === "master"
                    ? "main"
                    : branch.name.startsWith("feature/")
                    ? "feature"
                    : branch.name.startsWith("fix/")
                    ? "fix"
                    : branch.name.startsWith("dev")
                    ? "development"
                    : "other",
              })),
              selectedBranch: firstBranch?.name || "main",
              status:
                firstBranchStatus === 0
                  ? "Not Started"
                  : firstBranchStatus === 1
                  ? "In Progress"
                  : firstBranchStatus === 2
                  ? "Completed"
                  : "Failed",
              created_at: response.created_at,
            };
          });
  
          setRepositories(repoData);
          
        }
      } catch (error) {
        
      } finally {
        
      }
    };

  if (isImporting) {
    return (
      <div className="w-full flex items-center justify-center p-8">
        <div className="flex items-center space-x-3">
          <Loader2 className="w-6 h-6 text-blue-500 animate-spin" />
          <span className="text-gray-600">
            {isImporting ? 'Importing repositories...' : ''}
          </span>
        </div>
      </div>
    );
  }

  if(isLoading){
    return <TableLoadingSkeleton />
  }

  if (error) {
    return (
      <div className="w-full p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center space-x-2 text-red-600">
          <span className="text-sm">{error}</span>
          <button
            onClick={handleScanRepositories}
            className="text-sm underline hover:text-red-700"
          >
            Try again
          </button>
        </div>
      </div>
    );
  }

  const selectedCount = repositories.filter(repo => repo.selected).length;

  return (
    <div className="h-full flex flex-col overflow-hidden p-1">
      <div className="flex-shrink-0 px-4 py-1 ">
        <div className="flex justify-between items-center ">
          <h3 className="text-lg font-medium text-gray-900 py-1 -mt-2">
            Available Repositories ({repositories.length})
          </h3>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleScanRepositories}
              className="flex items-center space-x-2 px-3 py-1.5 text-sm bg-white border border-gray-300 rounded-md hover:bg-gray-50 transition-colors"
            >
              <RefreshCw className="w-4 h-4" />
              <span>Refresh</span>
            </button>
            <button
              onClick={handleImportRepositories}
              disabled={selectedCount === 0}
              className={`flex items-center space-x-2 px-4 py-1.5 text-sm rounded-md transition-colors
                ${selectedCount === 0 
                  ? 'bg-gray-100 text-gray-400 cursor-not-allowed' 
                  : 'bg-blue-500 text-white hover:bg-blue-600'}`}
            >
              <Check className="w-4 h-4" />
              <span>Import Selected ({selectedCount})</span>
            </button>
          </div>
        </div>
      </div>
      
      <div className="relative flex-grow">
        <RepositoryListTable
          repoWithKnowledge = {repositoriesVal}
          repositories={repositories}
          onToggleRepository={handleToggleRepository}
          onUpdateRepository={handleBranchChange}
          activeTabVal={activeTabVal}
          organization_name={organizationName}
        />
      </div>
    </div>
  );
}

export default ConnectedRepositorySection; 