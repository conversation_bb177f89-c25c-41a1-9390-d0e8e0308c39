import React, { useState, useContext } from 'react';
import { Code, Github, Gitlab, ChevronRight, RefreshCw } from 'lucide-react';
import PersonalRepositorySection from './PersonalRepositorySection';
import { ProjectAssetContext } from "@/components/Context/ProjectAssetContext";

function RepositoryAddition({ scmConfigurations, onSCMTypeSelect, addBtnClicked, onClose, onImport, repositories, setRepositories, setActiveTab, updateBuildStatuses, onRefresh }) {
  const [activeTabVal, setActiveTabVal] = useState('personal');
  const [showRepositories, setShowRepositories] = useState(false);
  const [showDetails, setShowDetails] = useState(false)
  const { setScanningRepoId, scanningRepoId } = useContext(ProjectAssetContext);

  const handleCloseModel = () => {
    setShowDetails(false)
    setScanningRepoId(null)
    if (addBtnClicked) {
      onClose();
    }
  }

  return (
    <div className="h-full mb-4">

      <div className="flex-shrink-0 px-4 py-4 -mt-2 flex items-center space-x-3 text-sm font-medium text-gray-700">
        {addBtnClicked && (
          <>
            <button
              className="flex items-center space-x-1 text-blue-600 hover:underline hover:text-blue-700 transition"
              onClick={onClose}
            >
              <span>Repository Lists</span>
            </button>
            <ChevronRight className="h-4 w-4 text-gray-400" />
          </>
        )}

        {(showRepositories || showDetails) && (
          <div className="flex items-center space-x-2">
            <button
              className="flex items-center space-x-1 text-blue-600 hover:underline hover:text-blue-700 transition"
              onClick={() => setShowRepositories(false)}
            >
              <span>Connection Details</span>
            </button>
            <ChevronRight className="h-4 w-4 text-gray-400" />
          </div>
        )}
      </div>

      <div className="-mt-4 h-full pb-3">
        {(activeTabVal === 'personal') ? (
          <PersonalRepositorySection 
            onSCMTypeSelect={onSCMTypeSelect} 
            onImport={onImport} 
            onClose={onClose} 
            setShowRepositories={setShowRepositories} 
            showRepositories={showRepositories} 
            addBtnClicked={addBtnClicked} 
            setRepositories={setRepositories} 
            repositories={repositories} 
            setActiveTab={setActiveTab} 
            showDetails={showDetails} 
            handleCloseModel={handleCloseModel} 
            updateBuildStatuses={updateBuildStatuses}
            handleRefresh={onRefresh}
          />
        ) : (
          <div className="max-w-lg mx-auto space-y-3">
            {scmConfigurations.length > 0 ? (
              scmConfigurations.map((config) => (
                <div
                  key={config.encrypted_scm_id}
                  className="group w-full p-4 text-left border border-gray-200 rounded-lg hover:border-gray-300 hover:bg-gray-50 transition-colors flex justify-between items-center "
                >
                  <div className="flex items-center space-x-3">
                    {config.scm_type === 'gitlab' ? (
                      <Gitlab className="w-6 h-6 text-gray-700" />
                    ) : (
                      <Github className="w-6 h-6 text-gray-700" />
                    )}
                    <div>
                      <div className='flex items-center space-x-2'>
                        <p className="font-medium text-gray-900">
                          {config.credentials.organization}
                        </p>
                        <span className="px-2 py-1 text-xs font-medium text-blue-600 bg-blue-50 rounded-full">
                          {config.scm_type.toUpperCase()}
                        </span>

                      </div>
                      <p className="text-sm text-gray-500">
                        {config.api_url || 'Default API URL'}
                      </p>
                    </div>

                  </div>
                  <button
                    onClick={() => handleScanRepo(config.encrypted_scm_id)}
                    className={`px-4 py-1.5 rounded-lg text-sm font-medium transition-all flex items-center space-x-1
                  ${scanningRepoId === config.encrypted_scm_id
                        ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                        : 'bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800'
                      }`}
                    disabled={scanningRepoId === config.encrypted_scm_id}
                  >
                    {scanningRepoId === config.encrypted_scm_id ? (
                      'Scanning...'
                    ) : (
                      <>
                        <RefreshCw className="w-4 h-4 mr-1" />
                        Scan Repo
                      </>
                    )}
                  </button>
                </div>
              ))
            ) : (
              <div className="text-center py-8">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-4 bg-gray-100 rounded-full">
                  <Code className="w-6 h-6 text-gray-400" />
                </div>
                <h3 className="mb-2 text-lg font-medium text-gray-900">
                  No Organization Accounts
                </h3>
                <p className="text-sm text-gray-500">
                  Contact your administrator to set up organization accounts.
                </p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}

export default RepositoryAddition; 