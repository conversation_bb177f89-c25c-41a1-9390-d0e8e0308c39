'use client'
import { useState } from 'react';
import { Info, X, AlertTriangle, Volume2, EyeOff } from 'lucide-react';
import { renderHTML } from '@/utils/helpers';
import { IconButton } from '../UIComponents/Buttons/IconButton';

interface BannerProps {
  id: string;
  type: 'alert' | 'maintenance' | 'announcement';
  message: string;
  detailedMessage?: string;
  showBanner: boolean;
  onAcknowledge?: (id: string, doNotShowAgain: boolean) => void;
}

export const Banner = ({
  id,
  type,
  message,
  detailedMessage,
  showBanner,
  onAcknowledge
}: BannerProps) => {
  const [isVisible, setIsVisible] = useState(showBanner);
  const [showModal, setShowModal] = useState<'detail' | 'confirm' | null>(null);

  if (!isVisible) return null;

  const getBannerStyles = () => {
    switch(type) {
      case 'alert':
        return 'bg-red-500 text-white';
      case 'maintenance':
        return 'bg-orange-500 text-white';
      case 'announcement':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  const getIcon = () => {
    switch(type) {
      case 'alert':
        return <AlertTriangle className="h-5 w-5" />;
      case 'maintenance':
        return <Info className="h-5 w-5" />;
      case 'announcement':
        return <Volume2 className="h-5 w-5" />;
      default:
        return <Info className="h-5 w-5" />;
    }
  };

  const handleSimpleClose = () => {
    setIsVisible(false);
  };

  const handleDoNotShow = () => {
    if (onAcknowledge) {
      onAcknowledge(id, true);
      setIsVisible(false);
      setShowModal(null);
    }
  };

  return (
    <>
      <div className={`${getBannerStyles()} shadow-lg relative transition-all duration-300`}>
        <div className="container mx-auto py-3 px-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-3 flex-1">
              <div className="rounded-full p-1.5 bg-white/20">
                {getIcon()}
              </div>
              <p className="font-medium">{message}</p>
            </div>

            <div className="flex items-center gap-2 shrink-0">
              {detailedMessage && (
                <button
                  onClick={() => setShowModal('detail')}
                  className="px-4 py-1.5 bg-white/20 rounded-md hover:bg-white/30 transition-colors text-sm"
                >
                  Learn More
                </button>
              )}
              <div className="flex items-center gap-1">
                {onAcknowledge && (
                  <IconButton
                    icon={<EyeOff className="w-5 h-5 text-white-600" />}
                    tooltip="Don't show again"
                    onClick={() => setShowModal('confirm')}
                    className="hover:text-black"
                  />
                )}
                <button
                  onClick={handleSimpleClose}
                  className="p-1.5 hover:bg-white/20 rounded-full transition-colors"
                  aria-label="Close"
                >
                  <X className="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Detailed Content Modal */}
      {showModal === 'detail' && (
        <div
          className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4"
          onClick={(e) => {
            if (e.target === e.currentTarget) setShowModal(null);
          }}
        >
          <div className="bg-white rounded-lg w-full max-w-lg max-h-[90vh] flex flex-col relative animate-modal-fade-in">
            {/* Header - Fixed */}
            <div className="flex justify-between items-center p-4 border-b">
              <h3 className="text-md font-semibold">{message}</h3>
              <button
                onClick={() => setShowModal(null)}
                className="p-1 hover:bg-gray-100 rounded-full transition-colors"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            {/* Content - Scrollable */}
            <div className="flex-1 overflow-y-auto p-4 min-h-[200px]">
              <div className="prose prose-sm max-w-none banner-sp652-div">
                <div dangerouslySetInnerHTML={{
                  __html: renderHTML(detailedMessage || '')
                    .replace(/'/g, '&#39;') // Use `&#39;` for single quotes
                    .replace(/"/g, '&quot;') // Use `&quot;` for double quotes
                }} />
              </div>
            </div>

            {/* Footer - Fixed */}
            <div className="border-t p-4">
              <div className="flex justify-end">
                <button
                  onClick={() => setShowModal(null)}
                  className="px-4 py-2 bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Don't Show Again Confirmation Modal */}
      {showModal === 'confirm' && (
        <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg max-w-sm w-full p-4 relative animate-modal-fade-in">
            <div className="flex items-center gap-3 mb-3">
              <div className="p-2 bg-blue-100 rounded-full">
                <EyeOff className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold">Hide Announcement</h3>
            </div>

            <p className="text-gray-600 text-sm mb-4">
              This will hide this announcement permanently. Are you sure?
            </p>

            <div className="flex justify-end gap-2">
              <button
                onClick={() => setShowModal(null)}
                className="px-3 py-1.5 text-sm text-gray-600 hover:bg-gray-100 rounded transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleDoNotShow}
                className="px-3 py-1.5 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
              >
                Yes, don't show again
              </button>
            </div>
          </div>
        </div>
      )}

      <style jsx>{`
        @keyframes modalFadeIn {
          from { opacity: 0; transform: scale(0.95); }
          to { opacity: 1; transform: scale(1); }
        }
        .animate-modal-fade-in {
          animation: modalFadeIn 0.2s ease-out;
        }
      `}</style>
    </>
  );
};
