import React from "react";
import SwaggerUIComponent from "../SwaggerUI/SwaggerUI";

const RequestResponseSection = ({ title, description, data }) => {
  if (!data || Object.keys(data).length === 0) return null;

  return (
    <div className="mb-4">
      <h4 className="text-sm font-medium text-gray-900 mb-2">{title}</h4>
      {description && (
        <h3 className="text-[12px] font-medium text-gray-900 mb-2">{description}</h3>
      )}
      <div className="bg-gray-50 rounded-md p-3">
        <pre className="text-sm text-gray-600 whitespace-pre-wrap overflow-x-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </div>
  );
};

const MethodBadge = ({ method }) => {
  const getMethodColor = (method) => {
    const colors = {
      GET: "bg-orange-100 text-orange-700",
      POST: "bg-green-100 text-green-700",
      PUT: "bg-yellow-100 text-yellow-700",
      DELETE: "bg-red-100 text-red-700",
      PATCH: "bg-purple-100 text-purple-700",
      default: "bg-gray-100 text-gray-700",
    };
    return colors[method] || colors.default;
  };

  return (
    <span
      className={`px-2 py-1 text-sm font-medium rounded-md ${getMethodColor(
        method
      )}`}
    >
      {method}
    </span>
  );
};

const Parameters = ({ parameters }) => {
  if (!parameters?.length) return null;


  return (
    <div className="mb-4">
      <h4 className="text-sm font-medium text-gray-900 mb-2">Parameters</h4>
      <div className="bg-gray-50 rounded-md p-3">
        <table className="min-w-full">
          <thead>
            <tr className="border-b border-gray-200">
              <th className="text-left text-sm font-medium text-gray-700 pb-2">
                Name
              </th>
              <th className="text-left text-sm font-medium text-gray-700 pb-2">
                Type
              </th>
              <th className="text-left text-sm font-medium text-gray-700 pb-2">
                Required
              </th>
              <th className="text-left text-sm font-medium text-gray-700 pb-2">
                Description
              </th>
            </tr>
          </thead>
          <tbody>
            {parameters.map((param, index) => (
              <tr
                key={index}
                className="border-b border-gray-100 last:border-0"
              >
                <td className="py-2 text-sm text-gray-900">{param.name}</td>
                <td className="py-2 text-sm text-gray-600">
                  {param.type || param.schema?.type || "string"}
                </td>
                <td className="py-2 text-sm text-gray-600">
                  {param.required ? "✓" : ""}
                </td>
                <td className="py-2 text-sm text-gray-600">
                  {param.description || "-"}
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );
};

const APIDocumentation = ({ apiDetails }) => {
  let parsedSwaggerAPI = "";
  const parseApiDetails = (details) => {
    if (!details) return null;

    try {
      let parsedData;

      if (typeof details === "string") {
        // Remove only the <JSON> opening and closing tags while keeping the JSON content
        const jsonContent = details.replace(/<\/?JSON>/g, "").trim();
        parsedData = JSON.parse(jsonContent);
        parsedSwaggerAPI = parsedData;
      } else {
        parsedData = details;
      }

      let mapData = parsedData.paths ? parsedData.paths : parsedData.endpoints ? parsedData.endpoints :  {}

      const extractRequestBody = (details) => {
        // First check if there's a requestBody defined
        if (details.requestBody) {
          return details.requestBody?.content?.["application/json"]?.schema || null;
        }

        // If no requestBody, check if any parameter is of type "body"
        const bodyParam = details.parameters?.find((p) => p.in === "body");
        return bodyParam?.schema || null;
      };

      const extractResponseSchema = (details) => {
        // First check if there's a response for 200 defined
        if (details.responses?.["200"]?.content) {
          return details.responses?.["200"]?.content?.["application/json"]?.schema || null;
        }

        // If no direct 200 response schema, check for any response that contains a schema
        const responseParam = details.responses?.["200"]?.schema || null;

        return responseParam?.schema ||responseParam || null;
      };

      const extractResponseDescription = (details) => {
        // First check if there's a response for 200 defined
        if (details.responses?.["200"]?.description) {
          return `Description: ${details.responses?.["200"]?.description}` || null;
        }

        // If no direct 200 response schema, check for any response that contains a schema
        const responseParam = `Description: ${details.responses?.["200"]?.description}` || null;

        return responseParam?.description ||responseParam || null;
      };


      const endpoints =
        Array.isArray(mapData) // If it's an array (for endpoints)
          ? mapData.flatMap((endpoint, index) => ({
              Path: endpoint.path,
              HTTP_method: endpoint.method.toUpperCase(),
              Description: endpoint.summary ? endpoint.summary : endpoint.description || "",
              parameters: endpoint.parameters || [],
              request_body:
                endpoint.parameters?.find((p) => p.in === "body")?.schema || null,
              response_description:
                endpoint.responses?.["200"]?.description || null,
              response_schema:
                endpoint.responses?.["200"]?.schema || null,
            }))
          : Object.entries(mapData).flatMap(([path, methods]) =>
              Object.entries(methods).map(([method, details]) => ({
                Path: path,
                HTTP_method: method.toUpperCase(),
                Description: details.summary || "",
                parameters: details.parameters || [],
                request_body: extractRequestBody(details),
                response_description: extractResponseDescription(details),
                response_schema: extractResponseSchema(details),
              }))
            );

      // Continue with the rest of the function...
      return {
        OpenAPIVer: `OpenAPI: ${parsedData.openapi?  parsedData.openapi : ""}`,
        API_name: `${parsedData?.info?.title? parsedData.info.title :"The API name  is not available "}`,
        API_version: `API Version: ${parsedData.apiVersion ? parsedData.apiVersion : parsedData.info?.version? parsedData.info.version:"Version information is not available" }`,
        API_description: `Description: ${parsedData.description? parsedData.description: parsedData.info.description? parsedData.info.description: ""}`,
        Base_Path: parsedData.basePath,
        endpoints,
      };
    } catch (error) {
      
      return null;
    }
  };
  const api = parseApiDetails(apiDetails);

  if (!api) {
    return (
      <div className="bg-gray-50 p-6 text-center">
        <p className="text-gray-500">No API data available.</p>
      </div>
    );
  }

  const apiSpec = parsedSwaggerAPI;

  return (
    <div className="w-full">
      <div className="bg-white rounded-lg border border-gray-200">
        {/* API Header */}
        {/*<div className="p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">
            {api.API_name}
          </h2>
          {api.OpenAPIVer && (
            <p className="mt-2 text-gray-600">{api.OpenAPIVer}</p>
          )}
          {api.API_version && (
            <p className="mt-2 text-gray-600">{api.API_version}</p>
          )}
          {api.API_description && (
            <p className="mt-2 text-gray-600">{api.API_description}</p>
          )}
          {api.Base_Path && (
            <p className="mt-2 text-sm text-gray-500">
              Base Path: {api.Base_Path}
            </p>
          )}
        </div>*/}

        {/* Endpoints */}
        <SwaggerUIComponent spec = {apiSpec}/>

        { /*<div className="p-6">
          <div className="space-y-4">
            {api.endpoints?.map((endpoint, index) => (
              <Accordion
                defaultOpen={true}
                key={`${endpoint.HTTP_method}-${endpoint.Path}-${index}`}
                title={
                  <div className="flex items-center space-x-3">
                    <MethodBadge method={endpoint.HTTP_method} />
                    <span className="font-medium text-gray-900">
                      {endpoint.Path}
                    </span>
                  </div>
                }
                icon={<Server className="h-5 w-5" />}
              >
                <div className="pt-4">
                  {endpoint.Description && (
                    <p className="text-gray-600 mb-4">{endpoint.Description}</p>
                  )}
                  <Parameters parameters={endpoint.parameters} />
                  {endpoint.request_body && (
                    <RequestResponseSection
                      title="Request Body Schema"
                      data={endpoint.request_body}
                      />
                    )}
                  {endpoint.response_schema && (
                    <RequestResponseSection
                      title="Response Schema"
                      description = {endpoint.response_description}
                      data={endpoint.response_schema}
                    />
                  )}
                </div>
              </Accordion>
            ))}
          </div>
        </div> */}
      </div>
    </div>
  );
};

export default APIDocumentation;
