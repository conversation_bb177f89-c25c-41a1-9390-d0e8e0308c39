@tailwind base;
@tailwind components;
@tailwind utilities;

// root css
@layer base {
  :root {
    --background: 255 100% 100%;
    --foreground: 222.2 84% 4.9%;

    --muted: 220 14.3% 95.9%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;

    --primary-50: 0 0% 98%;
    --primary-100: 0 0% 96%;
    --primary-200: 0 0% 93%;
    --primary-300: 0 0% 88%;
    --primary-400: 0 0% 74%;
    --primary-500: 0 0% 60%;
    --primary-600: 0 0% 45%;
    --primary-700: 0 0% 32%;
    --primary-800: 0 0% 20%;
    --primary-900: 0 0% 12%;
    --primary-950: 0 0% 5%;
    --primary: 0 0% 25%;
    --primary-foreground: 0 0% 98%;

    --secondary: 214.3 31.8% 91.4%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --accent: 214.3 31.8% 91.4%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;

    --ring: 24 94% 53%;

    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;

    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;

    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;

    --default-50: 210 40% 98%;
    --default-100: 210 40% 96.1%;
    --default-200: 214.3 31.8% 91.4%;
    --default-300: 212.7 26.8% 83.9%;
    --default-400: 215 20.2% 65.1%;
    --default-500: 215.4 16.3% 46.9%;
    --default-600: 215.3 19.3% 34.5%;
    --default-700: 215.3 25% 26.7%;
    --default-800: 217.2 32.6% 17.5%;
    --default-900: 222.2 47.4% 11.2%;
    --default-950: 222.2 84% 4.9%;

    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 47.4% 11.2%;
    --foreground: 210 40% 98%;

    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;

    --popover: 222.2 47.4% 11.2%;
    --popover-foreground: 210 40% 98%;

    --card: 215 27.9% 16.9%;
    --card-foreground: 210 40% 98%;

    --border: 215.3 25% 26.7%;
    --input: 215 27.9% 16.9%;

    --primary: 24 94% 53%;
    --primary-foreground: 60 9.1% 97.8%;

    --secondary: 215.3 25% 26.7%;
    --secondary-foreground: 210 40% 98%;

    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 85.7% 97.3;

    --ring: 24 94% 53%;

    --success: 142.1 70.6% 45.3%;
    --success-foreground: 138.5 76.5% 96.7%;

    --info: 188.7 94.5% 42.7%;
    --info-foreground: 183.2 100% 96.3%;

    --warning: 24.6 95% 53.1%;
    --warning-foreground: 33.3 100% 96.5%;

    --default-950: 210 40% 98%;
    --default-900: 210 40% 96.1%;
    --default-800: 214.3 31.8% 91.4%;
    --default-700: 212.7 26.8% 83.9%;
    --default-600: 215 20.2% 65.1%;
    --default-500: 215.4 16.3% 46.9%;
    --default-300: 215.3 19.3% 34.5%;
    --default-200: 215.3 25% 26.7%;
    --default-100: 217.2 32.6% 17.5%;
    --default-50: 222.2 47.4% 11.2%;
  }

  * {
    @apply border-custom-border;
  }

  html {
    @apply scroll-smooth;
  }

  body {
    @apply bg-custom-bg-primary dark:bg-background text-foreground text-sm;
  }

  /* Theme transition styles */
  .theme-transition {
    transition-property: background-color, color, border-color, fill, stroke;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }

  .theme-transition * {
    transition-property: background-color, color, border-color, fill, stroke, opacity, box-shadow;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
  }

  @keyframes slideDown {
    from {
      height: 0;
    }
    to {
      height: var(--radix-collapsible-content-height);
    }
  }

  @keyframes slideUp {
    from {
      height: var(--radix-collapsible-content-height);
    }
    to {
      height: 0;
    }
  }

  @keyframes progress-bar-stripes {
    0% {
      background-position: 1rem 0;
    }
    to {
      background-position: 0 0;
    }
  }

  .CollapsibleContent {
    overflow: hidden;
  }

  .CollapsibleContent[data-state="open"] {
    animation: slideDown 300ms ease-out;
  }

  .CollapsibleContent[data-state="closed"] {
    animation: slideUp 300ms ease-out;
  }

  .animate-stripes {
    animation: progress-bar-stripes 1s linear infinite;
  }
}