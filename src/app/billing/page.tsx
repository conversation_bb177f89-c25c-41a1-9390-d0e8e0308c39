"use client";

import { useState, useContext, useEffect, Suspense } from "react";
import { useUser } from "@/components/Context/UserContext";
import { Eye, EyeOff, Download } from "lucide-react";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import {
  getSettings,
  updateSettings,
  SettingModel,
  SecureSettingValue,
} from "@/utils/organization/settings";
import { decryptStringClipboard } from "@/utils/hash";

interface SettingSection {
  title: string;
  description: string;
  settings: SettingItem[];
}

interface SettingItem {
  section: string;
  key: string;
  label: string;
  description?: string;
  type: "toggle" | "checkbox" | "text" | "integer" | "dropdown";
  defaultValue: boolean | string | number;
  value: boolean | string | number;
  options?: string[];
}

// Add this skeleton loader component
const SettingsSkeleton = () => {
  return (
    <div className="w-full max-w-[1200px] animate-pulse">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <div className="h-6 w-32 bg-gray-200 rounded"></div>
          <div className="h-4 w-64 bg-gray-200 rounded mt-2"></div>
        </div>
        <div className="h-10 w-32 bg-gray-200 rounded"></div>
      </div>

      <div className="space-y-6">
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="mb-6">
            <div className="h-5 w-48 bg-gray-200 rounded"></div>
            <div className="h-4 w-96 bg-gray-200 rounded mt-2"></div>
          </div>

          <div className="grid grid-cols-2 gap-8">
            {[1, 2].map((i) => (
              <div key={i} className="flex flex-col space-y-2">
                <div className="h-4 w-32 bg-gray-200 rounded"></div>
                <div className="h-3 w-48 bg-gray-200 rounded"></div>
                <div className="h-10 w-full bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

// Wrap the main content in a separate component
const SettingsContent = (
  {
    /* ... props if needed ... */
  }
) => {
  const { is_admin } = useUser();
  const [formState, setFormState] = useState<SettingModel | null>(null);
  const [showToken, setShowToken] = useState(false);
  const [loading, setLoading] = useState(false);
  const { showAlert } = useContext(AlertContext);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settings = await getSettings();
        setFormState(settings);
      } catch (error) {
        showAlert("Failed to load settings", "error");
      }
    };
    fetchSettings();
  }, []);

  const getFigmaApiKey = () => {
    const figmaSettings = formState?.integrations?.figma;
    return figmaSettings?.find((s) => s.name === "figma_api_key")?.value || "";
  };

  const handleInputChange =
    (section: string, key: string) =>
    (e: React.ChangeEvent<HTMLInputElement>) => {
      setFormState((prev) => ({
        ...prev,
        integrations: {
          ...prev?.integrations,
          figma: [
            {
              name: "figma_api_key",
              value: e.target.value,
              secure: true,
            },
          ],
        },
      }));
    };

  const handleSave = async () => {
    setLoading(true);
    try {
      if (formState) {
        await updateSettings(formState);
        showAlert("Settings saved successfully", "success");
      } else {
        showAlert("No settings to save", "error");
      }
    } catch (error) {
      showAlert("Failed to save settings", "error");
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = async (setting: SecureSettingValue) => {
    const value = setting.secure
      ? decryptStringClipboard(setting.value)
      : setting.value;
    navigator.clipboard.writeText(value);
    showAlert("Token copied to clipboard", "success");
  };

  const sections: SettingSection[] = [
    {
      title: "Integration Settings",
      description: "Manage your external service connections",
      settings: [
        {
          section: "integrations",
          key: "figma_api_key",
          label: "Figma Access Token",
          description:
            "Enter your Figma access token to enable design integrations",
          type: "text",
          defaultValue: "",
          value: getFigmaApiKey(),
        },
      ],
    },
  ];

  const renderSettingInput = (setting: SettingItem) => {
    switch (setting.type) {
      case "text":
        return (
          <div className="relative">
            <input
              type={showToken ? "text" : "password"}
              value={String(
                showToken ? getSettingValue(setting) : setting.value
              )}
              onChange={handleInputChange(setting.section, setting.key)}
              placeholder={`Enter your ${setting.label}`}
              className="block w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 sm:text-sm pr-20"
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 space-x-2">
              <button
                type="button"
                onClick={() => setShowToken(!showToken)}
                className="text-gray-400 hover:text-gray-600"
              >
                {showToken ? (
                  <EyeOff className="h-5 w-5" />
                ) : (
                  <Eye className="h-5 w-5" />
                )}
              </button>
              {setting.section === "integrations" && getFigmaApiKey() && (
                <button
                  type="button"
                  onClick={() =>
                    copyToClipboard({
                      name: setting.key,
                      value: setting.value as string,
                      secure: setting.section === "integrations",
                    })
                  }
                  className="text-gray-400 hover:text-gray-600"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" />
                    <path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" />
                  </svg>
                </button>
              )}
            </div>
          </div>
        );
      default:
        return null;
    }
  };

  const getSettingValue = (setting: SettingItem): string => {
    const value = String(setting.value);
    return setting.section === "integrations"
      ? decryptStringClipboard(value)
      : value;
  };

  if (!formState) {
    return <SettingsSkeleton />;
  }

  return (
    <div className="w-full max-w-[1200px]">
      <div className="mb-6 flex justify-between items-center">
        <div>
          <h1 className="text-xl font-semibold">Billing & Usages</h1>
        </div>
      </div>

      {/* Free Plan Card */}
      <div className="mb-8 border rounded-lg p-6">
        <div className="flex justify-between items-center">
          <div>
            <h2 className="text-lg font-semibold">Free Plan</h2>
            <p className="text-sm text-gray-600">
              10000 Credits • Billing cycle renews in 5 days
            </p>
          </div>
          <div className="flex gap-3">
            <button className="px-4 py-2 border border-gray-300 rounded-md text-sm font-medium">
              Edit Payment Details
            </button>
            <button className="px-4 py-2 bg-blue-600 text-white rounded-md text-sm font-medium">
              Upgrade to Premium
            </button>
          </div>
        </div>
      </div>

      {/* Available Credits */}
      <div className="mb-8">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-base font-semibold">Available Credits</h2>
          <p className="text-sm">10000 of 5000 used</p>
        </div>
        <div className="border rounded-lg p-6">
          <div className="mb-2 h-2 bg-gray-100 rounded-full overflow-hidden">
            <div className="h-full bg-red-400 rounded-full w-[55%]"></div>
          </div>
          <div className="flex justify-between items-center">
            <div className="flex gap-4">
              <div className="flex items-center gap-1">
                <span className="h-2 w-2 rounded-full bg-red-400"></span>
                <span className="text-sm text-gray-600">Used</span>
              </div>
              <div className="flex items-center gap-1">
                <span className="h-2 w-2 rounded-full bg-green-200"></span>
                <span className="text-sm text-gray-600">Available</span>
              </div>
            </div>
            <button className="px-3 py-1 border border-gray-300 rounded-md text-sm font-medium">
              Manage Credits
            </button>
          </div>
        </div>
      </div>

      {/* Invoices */}
      <div>
        <h2 className="text-base font-semibold mb-2">Invoices</h2>
        <div className="border rounded-lg overflow-hidden">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-50 text-left text-sm text-gray-600">
                <th className="px-6 py-3 font-medium">REFERENCE</th>
                <th className="px-6 py-3 font-medium">TOTAL INCL. TAX</th>
                <th className="px-6 py-3 font-medium">STATUS</th>
                <th className="px-6 py-3 font-medium">DATE</th>
                <th className="px-6 py-3 font-medium"></th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-200">
              <tr className="text-sm">
                <td className="px-6 py-4">FO4522345-002</td>
                <td className="px-6 py-4">US$36.00</td>
                <td className="px-6 py-4">
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    PAID
                  </span>
                </td>
                <td className="px-6 py-4">23rd May 2025</td>
                <td className="px-6 py-4">
                  <button className="text-gray-500">
                    <Download size={18} />
                  </button>
                </td>
              </tr>
              <tr className="text-sm">
                <td className="px-6 py-4">FO4522345-001</td>
                <td className="px-6 py-4">US$36.00</td>
                <td className="px-6 py-4">
                  <span className="bg-green-100 text-green-800 text-xs px-2 py-1 rounded">
                    PAID
                  </span>
                </td>
                <td className="px-6 py-4">23rd March 2025</td>
                <td className="px-6 py-4">
                  <button className="text-gray-500">
                    <Download size={18} />
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

// Main component with lazy loading
export default function SettingsPage() {
  return (
    <Suspense
      fallback={
        <div className="w-full h-full flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        </div>
      }
    >
      <SettingsContent />
    </Suspense>
  );
}
