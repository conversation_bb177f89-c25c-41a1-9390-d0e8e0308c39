"use client"
import React, { useState, useEffect } from 'react'
import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> from '@/components/KaviaLongLogo'
import { useRouter } from 'next/navigation'
import CancelIcon from '../../../../../public/images/payment/cancel_icon.svg'
import Image from 'next/image'
import { handlePaymentCancel } from '@/utils/paymentAPI'
import { useSearchParams } from 'next/navigation'

function CancelPage() {
    const router = useRouter()
    const searchParams = useSearchParams()
    const [countdown, setCountdown] = useState(3)
    const [isProcessing, setIsProcessing] = useState(true)
    
    // Handle payment cancellation logic
    useEffect(() => {
        const processCancellation = async () => {
            try {
                const internal_secret_token = searchParams.get('internal_secret_token')
                if (!internal_secret_token) {
                    // setIsProcessing(false)
                    return
                }
                
                await handlePaymentCancel(internal_secret_token)
                setIsProcessing(false)
            } catch (error) {
                setIsProcessing(false)
            }
        }
        
        processCancellation()
    }, [searchParams])
    
    // Countdown and redirect
    useEffect(() => {
        if (isProcessing) return // Don't start countdown while processing
        
        const timer = countdown > 0 && setInterval(() => {
            setCountdown(countdown - 1)
        }, 1000)
        
        if (countdown === 0) {
            router.push('/')
        }
        
        return () => clearInterval(timer)
    }, [countdown, router, isProcessing])
    
    return (
        <div className='min-h-screen bg-[#1C1C1C] flex flex-col relative'>
            {/* Add the gradient overlay */}
            <div className="absolute inset-0 bg-[radial-gradient(50%_50%_at_50%_50%,#FF9358_0%,#231F20_100%)] opacity-60 blur-[514px] pointer-events-none" />
            
            {/* Logo */}
            <div className='flex items-center gap-2 p-[3vh] md:absolute md:top-[3vh] md:left-[3vw] relative z-10'>
                <KaviaLongLogo />
            </div>

            <div className='flex flex-col items-center justify-center flex-grow relative z-10'>
                <div className='w-full max-w-md p-8 bg-white rounded-lg shadow-lg text-center'>
                    {isProcessing ? (
                        <div className='flex flex-col items-center'>
                            <div className='animate-spin rounded-full w-[3rem] h-[3rem] border-t-2 border-b-2 border-orange-500 mb-4'></div>
                            <p className='text-gray-600'>Processing your cancellation...</p>
                        </div>
                    ) : (
                        <>
                            <div className='mb-6 flex justify-center'>
                                <div className='bg-red-100 rounded-full p-3'>
                                    <Image src={CancelIcon} alt="Cancelled" width={40} height={40} />
                                </div>
                            </div>
                            
                            <h1 className='text-2xl font-bold mb-4'>Payment Cancelled</h1>
                            <p className='text-gray-600 mb-6'>Your payment has been cancelled. No charges were made.</p>
                            
                            <p className='text-gray-600 mb-6'>
                                You will be redirected to the home page in {countdown} second{countdown !== 1 ? 's' : ''}...
                            </p>
                            
                            <button 
                                onClick={() => router.push('/')}
                                className='w-full py-2 px-4 bg-orange-500 hover:bg-orange-600 text-white font-semibold rounded-lg transition duration-200'
                            >
                                Go to Home Now
                            </button>
                        </>
                    )}
                </div>
            </div>
        </div>
    )
}

export default CancelPage