import React, { useContext, useEffect, useState } from 'react'
import Drawer from '@/components/Drawer'
import { useSearchParams,useParams } from "next/navigation";
import { useCodeGeneration } from '@/components/Context/CodeGenerationContext';
import { AlertContext } from '@/components/NotificationAlertService/AlertList';
import CreatePRModal from '@/components/Modal/CreatePRModal';
import { downloadRepository } from '@/utils/batchAPI';

interface Repository {
  name: string
  description: string
  lastSynced: string
  activeBranches: number
  current_branch: string
  organization: string
  isActive?: boolean
}

interface GitSidebarProps {
  isOpen: boolean
  onClose: () => void
  isConnected: boolean
  username?: string
}
interface CreatePRParams {
  sourceBranch: string;
  targetBranch: string;
  title: string;
  description: string;
}
const GitSidebar: React.FC<GitSidebarProps> = ({
  isOpen,
  onClose,
  isConnected = false,
  username = ''
}) => {
  const [autoCommit, setAutoCommit] = useState(true);
  const [autoPush, setAutoPush] = useState(false);
  const {
    wsConnection,
    gitStatus,
    gitCommandHistory,
    setGitCommandHistory,
    repositories,
    setRepositories,
    currentRepository,
    setCurrentRepository
  } = useCodeGeneration();

  const [activeRepoIndex, setActiveRepoIndex] = useState<number>(0);
  const activeRepository = repositories.length > 0 ? repositories[activeRepoIndex] : undefined;
  const [isCreatePRModalOpen, setIsCreatePRModalOpen] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const params= useParams();
  const projectId=params.projectId;
  const handleSwitchRepository = (index: number): void => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "switch_repository",
        task_id: taskId,
        input_data: { index }
      }));

      // Add a slight delay before refreshing git status to ensure repository switch completes
      setTimeout(() => {
        wsConnection.send(JSON.stringify({
          type: "git_status",
          task_id: taskId
        }));
      }, 500);
    }
    setActiveRepoIndex(index);
  };
  const searchParams = useSearchParams();
  const taskId = searchParams.get("task_id");
  const [branches, setBranches] = useState([]);

  const handleListRepositories = () => {
    if (wsConnection?.readyState === WebSocket.OPEN) {
      // setIsLoading(true);
      wsConnection.send(JSON.stringify({
        type: "list_repositories",
        task_id: taskId
      }));
      wsConnection.send(JSON.stringify({
        type: "list_branches",
        task_id: taskId,
        input_data: {
          send_message: false
        }
      }));
    }
  };
  // Modify your toggle handler functions:
  const handleAutoCommitToggle = () => {
    const newValue = !autoCommit; // Toggle the current value
    // setAutoCommit(newValue); // Update local state immediately for UI responsiveness
    if (newValue == false) {
      //  setAutoCommit(newValue)
      if (autoPush) {
        handleAutoPushToggle()
        setAutoPush(newValue)

      }
    }
    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "auto_commit_toggle",
        task_id: taskId,
        input_data: {
          value: newValue // Send the new value to the server
        }
      }));
    }
  };

  const handleAutoPushToggle = () => {
    const newValue = !autoPush; // Toggle the current value
    setAutoPush(newValue); // Update local state immediately

    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "auto_push_toggle",
        task_id: taskId,
        input_data: {
          value: newValue // Send the new value to the server
        }
      }));
    }
  };
  const cleanBranchName = (branchName: string): string => {
    // Remove the asterisk and surrounding whitespace if present
    return branchName.replace(/\s*\*\s*/, '');
  };

  const handleCreatePR = async ({
    sourceBranch,
    targetBranch,
    title,
    description
  }: CreatePRParams): Promise<void> => {
    const cleanedSourceBranch = cleanBranchName(sourceBranch);
    const cleanedTargetBranch = cleanBranchName(targetBranch);

    if (wsConnection?.readyState === WebSocket.OPEN) {
      wsConnection.send(JSON.stringify({
        type: "create_pr",
        task_id: taskId,
        input_data: {
          source_branch: cleanedSourceBranch,
          target_branch: cleanedTargetBranch,
          title,
          description
        }
      }));

      showAlert("Creating pull request...", "info");
      setIsCreatePRModalOpen(false);
    }
  };

  useEffect(() => {
    if (!wsConnection) return;
    //   // Define the message handler with proper type safety
    const handleMessage = (event: MessageEvent) => {
      // try {
      const data = JSON.parse(event.data);
      if (data.type === 'repository_list') {
        setRepositories(data.data.repositories);
      }
      if (data.type === 'git_controller_status') {
        setAutoCommit(data.data['auto-commit']);
        setAutoPush(data.data['auto-push']);
      }
      if (data.type === 'git_update' && data.data.type === 'branches') {
        setBranches(data.data.branches);
      }


    };

    //   // Add event listener
    wsConnection.addEventListener('message', handleMessage);


    handleListRepositories();

  }, [wsConnection, taskId, isOpen, isCreatePRModalOpen]);

  return (
    <div className='z-[60]'>
      <Drawer
        bodyClass='scrollbar-hide'
        isOpen={isOpen}
        onClose={onClose}
        placement="right"
        overlayClassName="z-[1001] left-0"
        width={400}
        title={
          <div className="flex items-center gap-2">
            <span className="font-medium">Source Control</span>
          </div>
        }
      >
        <div className="flex flex-col h-full py-[16px] px-[20px]">
          {/* Connection Status */}
          <div className="bg-green-50 p-4 rounded-md mb-6">
            <div className="flex items-center gap-2 mb-2">
              <svg viewBox="0 0 16 16" width="20" height="20" fill="currentColor" className="text-gray-700">
                <path fillRule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
              </svg>
              <h3 className="font-hind font-semibold text-[14px] leading-[150%] tracking-[0%] text-gray-800">GitHub {isConnected ? 'Connected' : 'Not Connected'}</h3>
            </div>
            {isConnected ? (
              // <p className='font-hind font-normal text-[13px] leading-[150%] tracking-[0%] text-gray-600'>Your account is connected to GitHub as <strong>{username}</strong></p>
              <></>
            ) : (
              <p className='font-hind font-normal text-[14px] leading-[150%] tracking-[0%] text-gray-800'>Connect your GitHub account to use advanced Git features and collaborate more efficiently with your team.</p>
            )}
          </div>

          {/* Repositories Section */}
          <div className="pb-4 mb-4">
            <div className="flex items-center justify-between border-b pb-2 mb-4">
              <h2 className="font-hind font-semibold text-[14px] leading-[150%] tracking-[0%] text-gray-800">Repositories</h2>
            </div>

            {repositories.length > 0 ? (
              <div className="space-y-4">
                {repositories.map((repo: Repository, index: number) => (
                  <div
                    key={`${repo.organization}/${repo.name}`}
                    className={`border rounded-md p-3 ${index === activeRepoIndex ? 'border-orange-500 bg-orange-50' : 'border-gray-200'}`}
                  >
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-hind font-semibold text-[14px] leading-[150%] tracking-[0%] text-gray-800 ">
                        {repo.organization}/{repo.name}
                      </p>
                      <div className='flex space-x-1'>
                      {index === activeRepoIndex ? (
                        <span className="bg-orange-100 text-orange-700 text-[11px] px-2 py-1 rounded-full">
                          Active
                        </span>
                      ) : (
                        <button
                          onClick={() => handleSwitchRepository(index)}
                          className="text-[12px] px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md"
                        >
                          Switch
                        </button>
                      )}

                      <button
                        className="text-[12px] px-3 py-1 bg-gray-100 hover:bg-gray-200 rounded-md flex items-center gap-1"
                      onClick={async () =>{
                        try{
                        await downloadRepository(taskId,projectId,repo.name)
                      }
                        catch{
                        
                        }}
                      }
                      >
                        <svg
                          width="14"
                          height="14"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4" />
                          <polyline points="7 10 12 15 17 10" />
                          <line x1="12" y1="15" x2="12" y2="3" />
                        </svg>
                      </button>
                      </div>
                    </div>


                    {index === activeRepoIndex ? (
                      <>
                        <p className="font-hind font-normal text-[13px] leading-[150%] tracking-[0%] text-gray-800">
                          {repo.description}
                        </p>
                        {/* <p className="font-hind font-normal text-[13px] leading-[150%] tracking-[0%] text-gray-600 mb-3">
                          Last synced: {repo.lastSynced}, {repo.activeBranches} active branches
                        </p> */}

                        {/* Pull Request Button for Active Repo */}
                        {isConnected && (
                          <button className="w-full bg-gray-800 text-white py-[8px] px-[30px] rounded-[6px] flex items-center justify-center gap-2 font-inter font-medium text-[12.9px] leading-[100%] tracking-[0%] text-center align-middle mb-3"
                            onClick={() => { setIsCreatePRModalOpen(!isCreatePRModalOpen) }}>
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor">
                              <path fillRule="evenodd" d="M7.177 3.073L9.573.677A.25.25 0 0110 .854v4.792a.25.25 0 01-.427.177L7.177 3.427a.25.25 0 010-.354zM3.75 2.5a.75.75 0 100 1.5.75.75 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122v5.256a2.251 2.251 0 11-1.5 0V5.372A2.25 2.25 0 011.5 3.25zM11 2.5h-1V4h1a1 1 0 011 1v5.628a2.251 2.251 0 101.5 0V5A2.5 2.5 0 0011 2.5zm1 10.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0zM3.75 12a.75.75 0 100 1.5.75.75 0 000-1.5z"></path>
                            </svg>
                            Create Pull Request
                          </button>
                        )}

                        {/* Current Branch for Active Repo */}
                        <div className="bg-gray-50 p-3 rounded-md flex items-center gap-2">
                          <svg viewBox="0 0 16 16" width="16" height="16" fill="currentColor">
                            <path fillRule="evenodd" d="M11.75 2.5a.75.75 0 100 1.5.75.75 0 000-1.5zm-2.25.75a2.25 2.25 0 113 2.122V6A2.5 2.5 0 0110 8.5H6a1 1 0 00-1 1v1.128a2.251 2.251 0 11-1.5 0V5.372a2.25 2.25 0 111.5 0v1.836A2.492 2.492 0 016 7h4a1 1 0 001-1v-.628A2.25 2.25 0 019.5 3.25zM4.25 12a.75.75 0 100 1.5.75.75 0 000-1.5zM3.5 3.25a.75.75 0 111.5 0 .75.75 0 01-1.5 0z"></path>
                          </svg>
                          <span className="font-mono">{repo.current_branch}</span>
                        </div>
                      </>
                    ) : (
                      <p className="font-hind font-normal text-[13px] leading-[150%] tracking-[0%] text-gray-600">
                        Branch {repo.current_branch}
                      </p>
                    )}
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-gray-600 text-center py-4">No repositories available</p>
            )}
          </div>

          {/* Git Automation Settings */}
          {isConnected && (
            <div className="border-b pb-4 mb-4">
              <h2 className="font-hind font-semibold text-[14px] leading-[150%] tracking-[0%] text-gray-800 border-b pb-2 mb-2">Git Automation Settings</h2>
              <div className="space-y-4">
                <div className="border-b flex items-center justify-between h-[40px] w-[340px]">
                  <span className="font-hind font-normal text-[14px] leading-[150%] tracking-[0%] text-gray-800">Auto-commit</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={autoCommit}
                      onChange={handleAutoCommitToggle}
                      className="sr-only peer"
                    />
                    <div className="w-[40px] h-[20px] bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-[16px] after:w-[16px] after:transition-all peer-checked:bg-orange-500"></div>
                  </label>
                </div>
                <div className="border-b flex items-center justify-between h-[40px] w-[340px]">
                  <span className="font-hind font-normal text-[14px] leading-[150%] tracking-[0%] text-gray-800">Auto-push</span>
                  <label className="relative inline-flex items-center cursor-pointer">
                    <input
                      type="checkbox"
                      checked={autoPush}
                      onChange={handleAutoPushToggle}
                      disabled={!autoCommit}
                      className="sr-only peer"
                    />
                    <div className="w-[40px] h-[20px] bg-gray-200 peer-focus:outline-none rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[3px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-[16px] after:w-[16px] after:transition-all peer-checked:bg-orange-500"></div>
                  </label>
                </div>
              </div>
            </div>
          )}

          {/* Connect Button */}
          {!isConnected && (
            <div className="mt-auto">
              <button className="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-md flex items-center justify-center gap-2">
                <svg viewBox="0 0 16 16" width="20" height="20" fill="currentColor">
                  <path fillRule="evenodd" d="M8 0C3.58 0 0 3.58 0 8c0 3.54 2.29 6.53 5.47 7.59.4.07.55-.17.55-.38 0-.19-.01-.82-.01-1.49-2.01.37-2.53-.49-2.69-.94-.09-.23-.48-.94-.82-1.13-.28-.15-.68-.52-.01-.53.63-.01 1.08.58 1.23.82.72 1.21 1.87.87 2.33.66.07-.52.28-.87.51-1.07-1.78-.2-3.64-.89-3.64-3.95 0-.87.31-1.59.82-2.15-.08-.2-.36-1.02.08-2.12 0 0 .67-.21 2.2.82.64-.18 1.32-.27 2-.27.68 0 1.36.09 2 .27 1.53-1.04 2.2-.82 2.2-.82.44 1.1.16 1.92.08 2.12.51.56.82 1.27.82 2.15 0 3.07-1.87 3.75-3.65 3.95.29.25.54.73.54 1.48 0 1.07-.01 1.93-.01 2.2 0 .21.15.46.55.38A8.013 8.013 0 0016 8c0-4.42-3.58-8-8-8z"></path>
                </svg>
                Connect to GitHub
              </button>
            </div>
          )}
        </div>
        {isCreatePRModalOpen && (
          <CreatePRModal
            isOpen={isCreatePRModalOpen}
            onClose={() => setIsCreatePRModalOpen(false)}
            onSubmit={handleCreatePR}
            branches={branches} // You might want to fetch branches from an API or context
            isLoading={false}
          />
        )}
      </Drawer>
    </div>
  )
}

export default GitSidebar