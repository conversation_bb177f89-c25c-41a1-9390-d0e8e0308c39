"use client"
import { useRouter, usePathname } from 'next/navigation';
import { useState, useEffect } from 'react';
import "@/app/globals.css";

// Add proper TypeScript interfaces
interface MenuItem {
  name: string;
  path: string;
}

interface MenuSection {
  // category: string;
  items: MenuItem[];
}

const menu: MenuSection[] = [
  {
    // category: "My Settings",
    items: [
      { name: "Profile", path: "/dashboard/profile/details"},
      { name: "Notifications", path: "/dashboard/profile/notifications" },
    ],
  },
  // {
  //   category: "Account Setup",
  //   items: [
  //     { name: "Integrations", path: "/users/settings/integrations"},
  //     { name: "Security", path: "/users/settings/security"},
  //   ],
  // },
  // {
  //   category: "Data Management",
  //   items: [
  //     // { name: "LLM Cost", path: "/users/settings/llm-cost" },
  //     { name: "Users & Teams", path: "/users/settings/data-users-teams"},
  //     { name: "Properties", path: "/users/settings/properties"},
  //     { name: "Import & Export", path: "/users/settings/import-export" },
  //   ],
  // },
  // {
  //   category: "Deployment",
  //   items: [
  //     { name: "Overview", path: "/users/settings/deployment-overview" },
  //     { name: "Deploy", path: "/users/settings/deploy-project"},
  //   ],
  // },
];

interface LayoutProps {
  children: React.ReactNode;
}

export default function RootLayout({ children }: Readonly<LayoutProps>) {
  const router = useRouter();
  const pathname = usePathname();
  const [isLoading, setIsLoading] = useState(false);
  const [activeItem, setActiveItem] = useState<string>('profile'); // Set default value directly

  useEffect(() => {
    // Get stored value with fallback
    const storedActiveItem = typeof window !== 'undefined' 
      ? sessionStorage.getItem('activeItem') || 'profile'
      : 'profile';
    setActiveItem(storedActiveItem);
  }, []);

  useEffect(() => {
    const lastSegment = pathname.split('/').pop();
    // Ensure lastSegment is never undefined
    const newActiveItem = lastSegment || 'profile';
    setActiveItem(newActiveItem);
    sessionStorage.setItem('activeItem', newActiveItem);
  }, [pathname]);

  const handleMenuItemClick = (path: string, uniqueKey: string) => {
    setActiveItem(uniqueKey);
    sessionStorage.setItem('activeItem', uniqueKey);
    router.push(path);
  };

  return (
    <>

      
      <div className="flex h-screen bg-gray-50">

        <div className="flex-1 flex flex-col overflow-hidden">
          {isLoading ? (
            <div className="flex items-center justify-center h-full">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500"></div>
            </div>
          ) : (
            <>

              <div className="flex-1 flex overflow-hidden">
                <nav className="w-80 border-r bg-white overflow-y-auto">
                  <div className="p-4">
                    {menu.map((section, idx) => (
                      <div key={idx} className="mb-6">
                        <div className="space-y-1">
                          {section.items.map((item) => {
                            const uniqueKey = item.path.split('/').pop() || '';
                            const isActive = activeItem === uniqueKey;
                            
                            return (
                              <button
                                key={uniqueKey}
                                onClick={() => handleMenuItemClick(item.path, uniqueKey)}
                                className={`w-full flex items-center space-x-3 px-4 py-2 text-sm rounded-lg transition-all duration-200
                                  ${isActive 
                                    ? 'bg-blue-50 text-blue-600 font-medium'
                                    : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                                  }`}
                              >
                                <span>{item.name}</span>
                              </button>
                            );
                          })}
                        </div>
                      </div>
                    ))}
                  </div>
                </nav>

                <main className="flex-1 overflow-y-auto custom-scrollbar bg-white p-6">
                  {children}
                </main>
              </div>
            </>
          )}
        </div>
      </div>
    </>
  );
}