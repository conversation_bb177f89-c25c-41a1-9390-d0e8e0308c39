"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect, useContext } from "react";
import { OrganizationTable } from "@/components/UserOnboarding/OrganizationTable";
import { getOrganizations, fetchOrganization, updateOrganizationPublicAccess } from "@/utils/api";
import { useUser } from "@/components/Context/UserContext";
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import DeleteConfirmationModal from "@/components/Modal/DeleteConfirmationModal";

interface ApiOrganization {
  id: string;
  name: string;
  domain: string;
  admin: {
    name: string;
    email: string;
    status: string;
    created_at: string;
  };
  status: string;
  cost: number;
  current_cost :number;
  credits: number;
  type: string;
}

interface Organization {
  id: string;
  orgName: string;
  domain: string;
  admin: string;
  email: string;
  status: string;
  onboardingDate: string;
  cost: string;
  currentCost:string
  credits: Number;
  remainingCredits: Number;
  type: string;
}

export default function OrganizationsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [isOpenToPublic, setIsOpenToPublic] = useState(false);
  const { showAlert } = useContext(AlertContext);
  const [toggleConfirmModalState, setToggleConfirmModalState] = useState<{
    isOpen: boolean;
    currentState: boolean;
  }>({
    isOpen: false,
    currentState: false
  });
  const [isConfirmModalLoading, setIsConfirmModalLoading] = useState(false);

  const {is_super_admin} = useUser();

  useEffect(() => {
    if(is_super_admin != null) {
      if (!is_super_admin) {
        router.replace('/home?tab=recentprojects');
      }
    }
  }, [is_super_admin]);

  useEffect(() => {
    const fetchOpenToPublicStatus = async () => {
      try {
        if (process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
          const rootOrg = await fetchOrganization(process.env.NEXT_PUBLIC_ROOT_TENANT_ID);
          setIsOpenToPublic(rootOrg.opentopublic || false);
        }
      } catch (err) {
        
      }
    };
    
    fetchOpenToPublicStatus();
  }, []);

  useEffect(() => {
    const fetchOrganizations = async () => {
      try {
        setIsLoading(true);
        const response = await getOrganizations();
        
        const transformedData = response.map((org: ApiOrganization): Organization => ({
          id: org.id,
          orgName: org.name,
          domain: org.domain,
          admin: org.admin.name,
          email: org.admin.email,
          status: org.status,
          cost: `$${org.cost !== undefined && org.cost !== null ? org.cost.toFixed(3) : '0.000'}`,
          currentCost: `$${org.current_cost !== undefined && org.current_cost !== null ? org.current_cost.toFixed(3) : '0.000'}`,
          credits: org.credits,
          remainingCredits: (org.credits - (org.cost !== undefined && org.cost !== null ? org.cost : 0) * 20) > 0 
            ? Math.round(org.credits - (org.cost !== undefined && org.cost !== null ? org.cost : 0) * 20) 
            : 0,
          type: org.type,
          onboardingDate: new Date(org.admin.created_at).toLocaleDateString(
            "en-US",
            {
              year: "numeric",
              month: "short",
              day: "numeric",
            }
          ),
        }));

        setOrganizations(transformedData);
      } catch (err) {
        setError(
          err instanceof Error
            ? err
            : new Error("Failed to fetch organizations")
        );
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizations();
  }, []);

  const handleAddOrganization = () => {
    // Implement navigation to add organization form
  };

  const handleFilter = () => {
    // Implement filter logic
  };

  const handleOpenToPublicToggle = () => {
    setToggleConfirmModalState({
      isOpen: true,
      currentState: isOpenToPublic
    });
  };

  const confirmOpenToPublicToggle = async () => {
    try {
      setIsConfirmModalLoading(true);
      
      if (process.env.NEXT_PUBLIC_ROOT_TENANT_ID) {
        const newStatus = !isOpenToPublic;
        
        // Make API call first, before updating UI
        const response = await updateOrganizationPublicAccess(process.env.NEXT_PUBLIC_ROOT_TENANT_ID, newStatus);
        
        if (response) {
          // Only update UI after successful API call
          setIsOpenToPublic(newStatus);
          showAlert(`Public access ${newStatus ? 'enabled' : 'disabled'} successfully`, "success");
        }
      }
    } catch (err) {
      
      showAlert("Failed to update public access status", "danger");
    } finally {
      setIsConfirmModalLoading(false);
      setToggleConfirmModalState({
        isOpen: false,
        currentState: false
      });
    }
  };

  return (
    <div className="">
      <div className="flex justify-between items-center mb-6">
        <h1 className="project-panel-heading">Organizations</h1>
        <div className="flex items-center">
          <label htmlFor="openToPublic" className="inline-flex items-center cursor-pointer mr-4">
            <input
              type="checkbox"
              id="openToPublic"
              className="sr-only"
              checked={isOpenToPublic || false}
              onChange={handleOpenToPublicToggle}
            />
            <div className={`relative w-11 h-6 bg-gray-200 rounded-full peer peer-focus:ring-4 peer-focus:ring-green-300 ${isOpenToPublic ? 'bg-green-500' : ''}`}>
              <div className={`absolute top-[2px] left-[2px] bg-white border border-gray-300 rounded-full h-5 w-5 transition-all ${isOpenToPublic ? 'translate-x-5' : ''}`}></div>
            </div>
            <span className="ml-3 text-sm font-medium text-gray-700">Open To Public</span>
          </label>
        </div>
      </div>

      <div className="bg-white rounded-lg shadow-sm">
        <OrganizationTable
          data={organizations}
          isLoading={isLoading}
          error={error || undefined}
          isB2C={false}
          isOpenToPublic={isOpenToPublic}
          onToggleOpenToPublic={handleOpenToPublicToggle}
        />
      </div>

      <DeleteConfirmationModal
        isOpen={toggleConfirmModalState.isOpen}
        onClose={() => {
          if (!isConfirmModalLoading) {
            setToggleConfirmModalState({ isOpen: false, currentState: toggleConfirmModalState.currentState });
          }
        }}
        onConfirm={confirmOpenToPublicToggle}
        title={`${toggleConfirmModalState.currentState ? 'Disable' : 'Enable'} Public Access`}
        message={`Are you sure you want to ${toggleConfirmModalState.currentState ? 'disable' : 'enable'} public access? This will ${toggleConfirmModalState.currentState ? 'restrict' : 'allow'} new users to sign up without invitations.`}
        isLoading={isConfirmModalLoading}
        confirmText={toggleConfirmModalState.currentState ? 'Disable' : 'Enable'}
        confirmButtonClass={toggleConfirmModalState.currentState ? 'bg-red-600 hover:bg-red-700' : 'bg-green-600 hover:bg-green-700'}
      />
    </div>
  );
}