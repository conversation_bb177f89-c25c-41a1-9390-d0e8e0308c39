// src/components/LivePreview/LivePreview.tsx
import { Calendar } from 'lucide-react';
import { Banner } from '@/components/Banner/Banner';
import { renderHTML } from '@/utils/helpers';

interface LivePreviewProps {
  previewMode: boolean;
  type: 'alert' | 'maintenance' | 'announcement';
  content: string;
  detailedContent: string;
  schedulePublish: boolean;
  hasExpiry: boolean;
  publishAt: Date | null;
  expiresAt: Date | null;
}

export const LivePreview = ({
  previewMode,
  type,
  content,
  detailedContent,
  schedulePublish,
  hasExpiry,
  publishAt,
  expiresAt
}: LivePreviewProps) => {

    const formatUTCDate = (date: Date | null) => {
        if (!date) return '';
        return date.toLocaleString('en-US', { 
          timeZone: 'UTC',
          dateStyle: 'medium',
          timeStyle: 'medium'
        }) + ' UTC';
      };
  return (
    <div className={`${previewMode ? 'col-span-2' : ''}`}>
      <div className="p-6 bg-white rounded-lg shadow-sm border h-full">
        <h2 className="text-lg font-semibold mb-4">Preview</h2>
        <Banner
          id="preview"
          type={type}
          message={content}
          detailedMessage={detailedContent}
          showBanner={true}
        />
        <div className="mt-2 banner-sp652-div">
          <span dangerouslySetInnerHTML={{__html: renderHTML(detailedContent)}} />
        </div>

        {(schedulePublish || hasExpiry) && (
  <div className="mt-4 space-y-2 text-sm text-gray-600">
    {schedulePublish && (
      <div className="flex items-center gap-2">
        <Calendar className="w-4 h-4" />
        <span>Scheduled: {formatUTCDate(publishAt)}</span>
      </div>
    )}
    {hasExpiry && (
      <div className="flex items-center gap-2">
        <Calendar className="w-4 h-4" />
        <span>Expires: {formatUTCDate(expiresAt)}</span>
      </div>
    )}
  </div>
)}
      </div>
    </div>
  );
};