// DashboardLayout.jsx
"use client";
import React, { useContext, useEffect, useState } from "react";
import {
  Chart as ChartJS,
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend,
} from "chart.js";
import { <PERSON>, Doughnut } from "react-chartjs-2";
import { getOrganizationCost, getOrganizationPackageCosts } from "@/utils/api";
import EmptyStateView from "@/components/Modal/EmptyStateModal";
import { useParams } from 'next/dist/client/components/navigation';
import { AlertContext } from "@/components/NotificationAlertService/AlertList";
import MetricsCalendar from "@/components/UserOnboarding/MetricsCalendar/MetricsCalendar";

ChartJS.register(
  ArcElement,
  CategoryScale,
  LinearScale,
  BarElement,
  LineElement,
  PointElement,
  Title,
  Tooltip,
  Legend
);

const styles = {
  dashboard: {
    maxWidth: "1600px",
    margin: "0 auto",
    padding: "24px",
  },
  header: {
    background: "linear-gradient(135deg, #2563eb, #3b82f6)",
    padding: "24px",
    borderRadius: "16px",
    marginBottom: "24px",
    boxShadow: "0 4px 15px rgba(37, 99, 235, 0.2)",
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
    color: "white",
  },
  selectorGroup: {
    display: "flex",
    gap: "12px",
    alignItems: "center",
  },
  selector: {
    padding: "10px 20px",
    borderRadius: "8px",
    border: "1px solid rgba(255, 255, 255, 0.3)",
    background: "rgba(255, 255, 255, 0.1)",
    color: "white",
    backdropFilter: "blur(4px)",
  },
  metricsGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(280px, 1fr))",
    gap: "24px",
    marginBottom: "24px",
  },
  metricCard: {
    padding: "24px",
    borderRadius: "16px",
    boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
    transition: "transform 0.3s ease",
    position: "relative",
    overflow: "hidden",
    background: "white",
  },
  purple: {
    background: "linear-gradient(135deg, #ffffff 0%, #f3f4ff 100%)",
    borderLeft: "4px solid #6366f1",
  },
  blue: {
    background: "linear-gradient(135deg, #ffffff 0%, #f0f7ff 100%)",
    borderLeft: "4px solid #3b82f6",
  },
  green: {
    background: "linear-gradient(135deg, #ffffff 0%, #f0fff4 100%)",
    borderLeft: "4px solid #10b981",
  },
  orange: {
    background: "linear-gradient(135deg, #ffffff 0%, #fff7ed 100%)",
    borderLeft: "4px solid #f97316",
  },
  metricValue: {
    fontSize: "32px",
    fontWeight: "600",
    margin: "12px 0",
  },
  metricLabel: {
    fontSize: "14px",
    color: "#6b7280",
    marginBottom: "8px",
  },
  metricTrend: {
    fontSize: "14px",
    color: "#6b7280",
  },
  chartGrid: {
    display: "grid",
    gridTemplateColumns: "repeat(auto-fit, minmax(450px, 1fr))",
    gap: "24px",
    marginBottom: "24px",
  },
  chartCard: {
    background: "white",
    padding: "24px",
    borderRadius: "16px",
    boxShadow: "0 4px 15px rgba(0, 0, 0, 0.1)",
    minHeight: "400px", // Added to maintain consistent height with EmptyState
  },
  chartHeader: {
    display: "flex",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: "24px",
  },
  chartTitle: {
    fontSize: "18px",
    fontWeight: "600",
    color: "#1f2937",
    marginBottom: "8px",
  },
  chartContainer: {
    height: "350px",
    position: "relative",
  },
  filterSelect: {
    appearance: "none",
    padding: "8px",
    borderRadius: "6px",
    border: "1px solid #e5e7eb",
    background: "white",
    fontSize: "14px",
    color: "#1f2937",
    cursor: "pointer",
  },
};

const DashboardLayout = () => {
  const { id } = useParams();
  const [organizationData, setOrganizationData] = useState(null);
  const [phaseData, setPhaseData] = useState(null);
  
  const todaysDate = new Date();
  const[startDate, setStartDate] = useState();
  const[endDate, setEndDate] = useState();
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const {showAlert} = useContext(AlertContext);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const data = await getOrganizationCost(id);
        const phase_data = await getOrganizationPackageCosts(id);
        setOrganizationData(data);
        setPhaseData(phase_data)
      } catch (error) {
        setOrganizationData(null);
        // showAlert(error.message, "danger");
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  const handleDateChange = (label, value) => {
    if(label === "start"){
      const firstDayOfMonth = new Date(value.getFullYear(), value.getMonth(), 1);
      firstDayOfMonth.setHours(0, 0, 0, 0); // Set time to 00:00:00
      setStartDate(firstDayOfMonth);
    }
    else{
      const lastDayOfMonth = new Date(value.getFullYear(), value.getMonth() + 1, 0);
      lastDayOfMonth.setHours(23, 59, 59, 999); // Set time to 23:59:59
      setEndDate(lastDayOfMonth);
    }
  };

  useEffect(() => {
    // Only perform the date adjustment if both dates are valid
    if (startDate && endDate && startDate > endDate) {
      handleDateChange("end", startDate);
    }
  }, [startDate, endDate]);

  useEffect(() => {
    handleDateChange("start", todaysDate);
    handleDateChange("end", todaysDate);
  }, [])

  const LoadingSkeleton = () => (
    <div style={styles.dashboard}>
      <div style={styles.skeletonHeader} className="animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
      </div>
      <div style={styles.metricsGrid}>
        {[1, 2, 3, 4].map((item) => (
          <div key={item} className="animate-pulse" style={styles.metricCard}>
            <div className="h-4 bg-gray-200 rounded w-1/2 mb-4"></div>
            <div className="h-8 bg-gray-200 rounded w-3/4"></div>
          </div>
        ))}
      </div>
      <div style={styles.chartGrid}>
        {[1, 2].map((item) => (
          <div key={item} className="animate-pulse" style={styles.chartCard}>
            <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
            <div className="h-64 bg-gray-200 rounded"></div>
          </div>
        ))}
      </div>
    </div>
  );

  if (loading) return <LoadingSkeleton />;
  if (error) return <div style={styles.loadingError}>Error: {error}</div>;
  if (!organizationData) return <EmptyStateView type="noData" />;

  const hasDataForPeriod = (data) => {
    if (!data || !data.datasets) return false;
    return data.datasets.some((dataset) =>
      dataset.data.some((value) => value > 0)
    );
  };

  

  const getFilteredCosts = () => {
    let totalCost = 0;
    organizationData.users.forEach((user) => {
      user.projects.forEach((project) => {
        project.agents?.forEach((agent) => {
          Object.entries(agent.costs_by_date || {}).forEach(([date, cost]) => {
            const dateObj = new Date(date);
            if (
              dateObj >= startDate && dateObj <=endDate
            ) {
              totalCost += parseFloat(cost.replace("$", ""));
            }
          });
        });
      });
    });
    return totalCost;
  };

  const getFilteredUsers = () => {
    let userCount = 0;
    organizationData.users.forEach((user) => {
      let userFound = false;
      if(userFound) return; //goes to next user if user found even in any one costs-by-date
      user.projects.forEach((project) => {
        if(userFound) return;
        project.agents?.forEach((agent) => {
          if(userFound) return;
          Object.entries(agent.costs_by_date || {}).forEach(([date,cost]) => {
            if(userFound) return;
            const dateObj = new Date(date);
            if (
              dateObj >= startDate && dateObj <=endDate
            ) {
              userCount += 1;
              userFound = true
            }
          })
        })
      })
    })
    return userCount;
  }

  const getFilteredProjects = () => {
    let projectCount = 0;
    organizationData.users.forEach((user) => {
      user.projects.forEach((project) => {
        let projectFound = false;
        if(projectFound) return;
        project.agents?.forEach((agent) => {
          if(projectFound) return;
          Object.entries(agent.costs_by_date || {}).forEach(([date,cost]) => {
            if(projectFound) return;
            const dateObj = new Date(date);
            if (
              dateObj >= startDate && dateObj <=endDate
            ) {
              projectCount += 1;
              projectFound = true
            }
          })
        })
      })
    })
    return projectCount;
  }

  const prepareAgentCostData = () => {
    const agentCosts = {};
    let totalCost = 0;

    organizationData.users.forEach((user) => {
      user.projects.forEach((project) => {
        project.agents?.forEach((agent) => {
          Object.entries(agent.costs_by_date || {}).forEach(([date, cost]) => {
            const dateObj = new Date(date);
            if (
              dateObj >= startDate && dateObj <=endDate
            ) {
              const costValue = parseFloat(cost.replace("$", ""));
              totalCost += costValue;

              const agentType = agent.agent_name.includes("Agent")
                ? agent.agent_name.replace("Agent", "")
                : agent.agent_name;

              if (!agentCosts[agentType]) {
                agentCosts[agentType] = 0;
              }
              agentCosts[agentType] += costValue;
            }
          });
        });
      });
    });

    const significantAgents = Object.entries(agentCosts)
      .map(([agent, cost]) => ({
        agent,
        cost,
        percentage: (cost / (totalCost || 1)) * 100,
      }))
      .filter((item) => item.percentage > 0)
      .sort((a, b) => b.cost - a.cost);

    return {
      labels: significantAgents.map(
        (item) => `${item.agent} (${item.percentage.toFixed(1)}%)`
      ),
      datasets: [
        {
          data: significantAgents.map((item) => item.cost),
          backgroundColor: [
            "#4F46E5",
            "#10B981",
            "#F59E0B",
            "#EF4444",
            "#6B7280",
          ],
        },
      ],
    };
  };

  const preparePackageCostData = () => {
    const packageCosts = {};
    let totalCost = 0;
  
    phaseData.users.forEach((user) => {
      user.projects.forEach((project) => {
        project.packages.forEach((pkg) => {
          pkg.agents.forEach((agent) => {
            Object.entries(agent.costs_by_date || {}).forEach(([date, cost]) => {
              const dateObj = new Date(date);
              if (dateObj >= startDate && dateObj <= endDate) {
                const costValue = parseFloat(cost.replace("$", ""));
                totalCost += costValue;

                const packageName = pkg.package_name;

                if (!packageCosts[packageName]) {
                  packageCosts[packageName] = 0;
                }
                packageCosts[packageName] += costValue;
              }
            });
          });
        });
      });
    });
  
    const significantPackages = Object.entries(packageCosts)
      .map(([pkgName, cost]) => ({
        packageName: pkgName,
        cost,
        percentage: (cost / (totalCost || 1)) * 100,
      }))
      .sort((a, b) => b.cost - a.cost);
  
    return {
      labels: significantPackages.map(
        (item) => `${item.packageName} (${item.percentage.toFixed(1)}%)`
      ),
      datasets: [
        {
          data: significantPackages.map((item) => item.cost),
          backgroundColor: [
            "#4F46E5",
            "#10B981",
            "#F59E0B",
            "#EF4444",
            "#6B7280",
          ],
        },
      ],
    };
  };
  

  const prepareDailyCostData = () => {
    const userCosts = organizationData.users
      .map((user) => {
        const totalCost = user.projects.reduce(
          (projectTotal, project) =>
            projectTotal +
            project.agents?.reduce((agentTotal, agent) => {
              const filteredCost = Object.entries(agent.costs_by_date || {})
                .filter(([date]) => {
                  const dateObj = new Date(date);
                  return (
                    dateObj >= startDate && dateObj <=endDate
                  );
                })
                .reduce(
                  (sum, [, cost]) => sum + parseFloat(cost.replace("$", "")),
                  0
                );
              return agentTotal + filteredCost;
            }, 0),
          0
        );

        return {
          user: user.user_name,
          cost: totalCost,
        };
      })
      .filter((item) => item.cost > 0);

    return {
      labels: userCosts.map((item) => item.user),
      datasets: [
        {
          label: "User Costs",
          data: userCosts.map((item) => item.cost),
          backgroundColor: "#3b82f6",
          borderColor: "#2563eb",
          borderWidth: 1,
        },
      ],
    };
  };

  const prepareProjectCostData = () => {
    const projects = organizationData.users.flatMap((user) => user.projects);
    const projectTitles = [
      ...new Set(projects.map((project) => project.project_title)),
    ];

    const filteredProjects = projects.map((project) => {
      const filteredAgents = project.agents?.map((agent) => {
        const filteredCost = Object.entries(agent.costs_by_date || {})
          .filter(([date]) => {
            const dateObj = new Date(date);
            return (
              dateObj >= startDate && dateObj <=endDate
            );
          })
          .reduce(
            (sum, [, cost]) => sum + parseFloat(cost.replace("$", "")),
            0
          );
        return { ...agent, total_cost: `$${filteredCost}` };
      });
      return { ...project, agents: filteredAgents };
    });

    const agentTypes = [
      ...new Set(
        filteredProjects.flatMap((project) =>
          project.agents?.map((agent) => agent.agent_name)
        )
      ),
    ];

    const datasets = agentTypes
      .filter((agentType) => {
        const totalAgentCost = filteredProjects.reduce((sum, project) => {
          const agentCost = project.agents?.find(
            (a) => a.agent_name === agentType
          );
          return (
            sum +
            (agentCost ? parseFloat(agentCost.total_cost.replace("$", "")) : 0)
          );
        }, 0);
        return totalAgentCost > 0;
      })
      .map((agentType, index) => {
        const data = projectTitles.map((title) => {
          const project = filteredProjects.find(
            (p) => p.project_title === title
          );
          if (!project) return 0;

          const agentCost = project.agents?.find(
            (agent) => agent.agent_name === agentType
          );
          return agentCost
            ? parseFloat(agentCost.total_cost.replace("$", ""))
            : 0;
        });

        return {
          label: agentType,
          data,
          backgroundColor: `hsl(${index * 137.508}deg, 70%, 50%)`,
        };
      });

    return {
      labels: projectTitles,
      datasets,
    };
  };

  const getFilteredTokens = () => {
    return organizationData.users?.reduce((acc, user) => {
      return (
        acc +
        user.projects?.reduce((projectAcc, project) => {
          return (
            projectAcc +
            project.agents?.reduce((agentAcc, agent) => {
              return(
                agentAcc +
                Object.entries(agent?.tokens_by_date || {})
                  .filter(([date]) => {
                    const dateObj = new Date(date);
                    return (
                      dateObj >= startDate && dateObj <= endDate
                    );
                  })
                  .reduce(
                    (sum, [, tokens]) => 
                      sum +
                      (Number(tokens.input_tokens) || 0) +
                      (Number(tokens.output_tokens) || 0),
                    0
                  )
              );
            }, 0)
          );
        }, 0) 
      );
    }, 0);
  };

  return (
    <div style={styles.dashboard}>
      <header>
        <div className="flex justify-between items-center mb-6">
          <div className="text-2xl font-semibold">
            {organizationData.organization_name}
          </div>
          <div className="flex gap-10 mt-auto">
            <MetricsCalendar
              label="From:"
              date={startDate}
              onChange={(value) => handleDateChange("start", value)}
            />
            <MetricsCalendar
              label="To:"
              date={endDate}
              startDate={startDate}
              onChange={(value) => handleDateChange("end", value)}
            />
          </div>
        </div>
      </header>

      <div style={styles.metricsGrid}>
        <MetricCard
          label="Active Users"
          value={getFilteredUsers()}
          color="orange"
        />
        <MetricCard
          label="Active Projects"
          value={getFilteredProjects()}
          color="blue"
        />
        <MetricCard
          label="Total Organization Cost"
          value={`$${getFilteredCosts().toFixed(2)}`}
          color="purple"
        />
        <MetricCard
          label="Total Token Consumption"
          value={getFilteredTokens()}
          color="green"
        />
      </div>

      <div style={styles.chartGrid}>
        <ChartCard title="Agent Cost Distribution">
          {hasDataForPeriod(prepareAgentCostData()) ? (
            <Doughnut
              data={prepareAgentCostData()}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      padding: 20,
                      usePointStyle: true,
                    },
                  },
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        return ` $${context.raw.toFixed(2)}`;
                      },
                    },
                  },
                },
                cutout: "60%",
              }}
            />
          ) : (
            <EmptyStateView type="agentCostDistribution" />
          )}
        </ChartCard>

        <ChartCard title="Phase Cost Distribution">
          {hasDataForPeriod(preparePackageCostData()) ? (
            <Doughnut
              data={preparePackageCostData()}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: {
                    position: "right",
                    labels: {
                      padding: 20,
                      usePointStyle: true,
                    },
                  },
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        return ` $${context.raw.toFixed(2)}`;
                      },
                    },
                  },
                },
                cutout: "60%",
              }}
            />
          ) : (
            <EmptyStateView type="phaseCostDistribution" />
          )}
        </ChartCard>

        <ChartCard title="User Cost Analysis">
          {hasDataForPeriod(prepareDailyCostData()) ? (
            <Bar
              data={prepareDailyCostData()}
              options={{
                responsive: true,
                maintainAspectRatio: false,
                minBarThickness: 10,
                barThickness: 20,
                maxBarThickness: 30,
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    callbacks: {
                      label: function (context) {
                        return `Cost: $${context.raw.toFixed(2)}`;
                      },
                    },
                  },
                },
                scales: {
                  x: {
                    ticks: {
                      autoSkip: true,
                      maxRotation: 45,
                      minRotation: 45,
                    },
                  },
                  y: {
                    beginAtZero: true,
                    ticks: {
                      callback: (value) => `$${value}`,
                    },
                  },
                },
              }}
            />
          ) : (
            <EmptyStateView type="userCostAnalysis" />
          )}
        </ChartCard>
      </div>

      <ChartCard title="Project Cost Analysis">
        {hasDataForPeriod(prepareProjectCostData()) ? (
          <Bar
            data={prepareProjectCostData()}
            options={{
              responsive: true,
              maintainAspectRatio: false,
              barThickness: 20, // Control bar width
              maxBarThickness: 30, // Maximum bar width
              plugins: {
                legend: {
                  position: "top",
                },
                tooltip: {
                  callbacks: {
                    label: function (context) {
                      return `${context.dataset.label}: $${context.raw.toFixed(
                        2
                      )}`;
                    },
                  },
                },
              },
              scales: {
                x: {
                  stacked: true,
                },
                y: {
                  stacked: true,
                  beginAtZero: true,
                  ticks: {
                    callback: (value) => `$${value}`,
                  },
                },
              },
            }}
          />
        ) : (
          <EmptyStateView type="projectCostAnalysis" />
        )}
      </ChartCard>
    </div>
  );
};

const MetricCard = ({ label, value, color }) => (
  <div style={{ ...styles.metricCard, ...styles[color] }}>
    <div style={styles.metricLabel}>{label}</div>
    <div style={styles.metricValue}>{value}</div>
  </div>
);

const ChartCard = ({ title, children }) => (
  <div style={styles.chartCard}>
    <div style={styles.chartHeader}>
      <h3 style={styles.chartTitle}>{title}</h3>
    </div>
    <div style={styles.chartContainer}>{children}</div>
  </div>
);

export default DashboardLayout;
