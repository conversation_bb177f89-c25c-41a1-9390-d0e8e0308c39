"use client";

import { useRouter, useSearchParams } from "next/navigation";
import { removeCookie, getCookie } from "@/utils/auth";
import { useEffect, useState } from "react";
import LoginSignupContainer from "@/components/LoginSignupContainer";
import { getUser } from "@/utils/api";

export default function WarningPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const isSigninSuccess = searchParams.get('signin') === 'true';
  const [isChecking, setIsChecking] = useState(true);
  const [showSuccess, setShowSuccess] = useState(isSigninSuccess);

  useEffect(() => {
    // Handle signin success message
    if (isSigninSuccess) {
      const timer = setTimeout(() => {
        setShowSuccess(false);
        // Construct the current URL without the signin parameter
        const url = new URL(window.location.href);
        url.searchParams.delete('signin');
        router.replace(url.pathname + url.search);
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [isSigninSuccess, router]);

  useEffect(() => {
    const checkUserStatus = async () => {
      try {
        setIsChecking(true);

        // First check if inactive_tenant cookie exists
        const inactiveTenant = await getCookie("inactive_tenant");
        if (!inactiveTenant) {
          // If cookie doesn't exist, redirect to login
          router.push("/users/login");
          return;
        }

        // Try to check if user is active, but don't worry if it fails
        try {
          const userData = await getUser();

          // If tenant is active
          if (!userData.error) {
            // Remove inactive_tenant cookie and redirect to home
            await removeCookie("inactive_tenant");
            router.push("/");
          } else if (userData.errorType === "INACTIVE_TENANT") {

          }
        } catch (error: any) {
          // For other errors, log them but stay on page
          
        }
      } finally {
        setIsChecking(false);
      }
    };

    checkUserStatus();
  }, [router]);

  const handleLogout = async () => {
    try {
      // Clear all auth cookies
      await removeCookie("idToken");
      await removeCookie("refreshToken");
      await removeCookie("inactive_tenant");
      await removeCookie("tenant_id");
      await removeCookie("encrypted_tenant_id");

      // Redirect to login page
      router.push("/users/login");
    } catch (error) {
      
      // Still try to redirect even if cookie removal fails
      router.push("/users/login");
    }
  };

  return (
    <LoginSignupContainer>
      <div className="bg-black/80 rounded-lg shadow-xl p-7 max-w-md w-full z-20 text-center border border-[#F26A1B]/30 backdrop-blur-sm">
        <h1 className="mb-2 -mt-2 text-xl font-semibold text-white">
          {showSuccess ? "Login Successful" : "Activation Pending"}
        </h1>

        <div className="flex flex-col items-center justify-center py-6">
          {showSuccess ? (
            <div className="w-12 h-12 bg-green-500/20 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-green-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>
          ) : (
            <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-4">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-primary"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"
                />
              </svg>
            </div>
          )}

          {showSuccess ? (
            <p className="text-gray-300 mb-3">
              Your account has been created successfully!
            </p>
          ) : (
            <p className="text-gray-300 mb-3">
              Your account is currently pending approval.
            </p>
          )}

          {showSuccess ? (
            <p className="text-gray-400 text-sm mb-6">
              Account setup is complete. You can now proceed.
            </p>
          ) : (
            <p className="text-gray-400 text-sm mb-6">
              An administrator will review your registration and contact you via email once access is granted.
              Please check your inbox for updates.
            </p>
          )}

          <button
            onClick={handleLogout}
            className="px-4 py-2 bg-gradient-to-r from-[#F26A1B] to-[#FF9358] text-white rounded-md hover:from-[#FF9358] hover:to-[#F26A1B] transition-all w-full"
          >
            Return to Login
          </button>
        </div>
      </div>
    </LoginSignupContainer>
  );
}