/* architecture tab header alignment starts */
/* .systemContextContentWrapper { */
.system-context-wrapper {
    @apply overflow-y-auto max-h-[74vh] text-black text-[15px] font-normal leading-[21px] tracking-[0.2px]
}
/* .systemContextSubContainer { */
.system-context-sub-wrapper {
    @apply mb-6 bg-white p-0
}
/* .systemContextOuterBorder { */
.system-context-container {
    /* @apply p-4 space-y-4 */
    @apply space-y-4
}

/* .systemContextHeader { */
.system-context-header {
    @apply flex sticky top-0 p-2 justify-between  items-center border rounded-lg h-[80px] z-10
}

.headerDiv {
    @apply flex items-center flex-grow max-h-full flex-wrap ml-2
}

.headingTitle {
    @apply text-[16px] font-bold font-[Hind] text-[#2A3439] whitespace-normal max-w-[400px] break-words
}
.badge {
    @apply py-1 ml-2
}

.buttons {
    @apply flex items-center justify-end flex-shrink-0
}

.relatedComponentDiv {
    /* @apply mt-8 mx-5 */
}
.relatedComponent {
    @apply text-[16px] font-bold font-[Hind] text-[#2A3439] mb-4
}
.notFound {
    @apply flex items-center justify-center -mt-[5px]
}


/* system context empty state */
.system-context-empty-state {
    @apply flex items-center justify-center h-full w-full
}