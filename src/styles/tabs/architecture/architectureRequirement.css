/* architectureRequirement page alignment starts */
 
/* .architectureRequirementContainer{ */
.architecture-requirement-wrapper {
    @apply overflow-y-auto max-h-[74vh] text-black text-[15px] font-normal leading-[21px] tracking-[0.2px]
}
/* .architectureRequirementMainTab{ */
.architecture-requirement-sub-wrapper { 
    @apply mb-6 bg-white p-0
}

.architecture-requirement-container {
    /* @apply p-4 space-y-4 */
    @apply space-y-4
}

/* .architectureRequirementHeader { */
.architecture-requirement-header {
    @apply flex sticky top-0 p-2 justify-between  items-center border rounded-lg h-[80px] z-10
}
.architecture-requirement-heading-wrapper {
    @apply flex items-center flex-grow max-h-full flex-wrap ml-2
}
.architecture-requirement-heading-title {
    @apply text-[16px] font-bold  text-[#2A3439] whitespace-normal max-w-[400px] break-words
}

.architecture-requirement-header-badge {
    @apply py-1 ml-2
}

.architecture-requirement-header-button-wrapper {
    @apply flex items-center justify-end flex-shrink-0 mr-2 space-x-2
}

.architecture-requirement-related-child-nodes {
    @apply mt-3
}
.architecture-requirement-related-child-node-text {
    @apply text-[16px] font-bold  text-[#2A3439] mb-4
}

.architecture-requirement-related-child-node-not-found {
    @apply flex items-center justify-center -mt-[8px]
}

.architecture-requirement-not-found {
    @apply flex justify-center  items-center text-center
}

/* architectureRequirement css alignment ends */

/* architecture requirements child node css alignments start */



.architecture-requirement-child-node-wrapper {
    @apply mb-6 bg-white
}

/* .architecture-requirement-child-node-outer-border {
    @apply border rounded-lg p-4 shadow-lg
} */
.architecture-requirement-child-node-header {
    @apply flex sticky top-0 p-2 justify-between  items-center border rounded-lg h-[80px]
}

.architecture-requirement-child-node-back-button {
    @apply mr-4 p-2 hover:bg-gray-200 rounded-full transition-colors duration-200
}
.architecture-requirement-child-node-back-button-wrapper {
    @apply bg-gray-200 rounded-full p-2 inline-flex items-center justify-center
}

.architecture-requirement-child-node-back-button-icon {
    @apply h-5 w-5 text-gray-700
}
.architecture-requirement-child-node-title-wrapper {
    @apply flex items-center space-x-2
}
.architecture-requirement-child-node-title {
    @apply mr-3 text-[16px] font-bold font-[Hind] text-[#2A3439]
}

.architecture-requirement-child-node-description-wrapper {
    @apply mt-2
}

.architecture-requirement-child-node-description-title{
    @apply text-[16px] font-bold font-[Hind] text-[#2A3439]
}

.architecture-requirement-child-node-description {
    @apply mt-1
}

.architecture-requirement-child-node-related-node {
    @apply mt-8
}

.architecture-requirement-child-node-related-node-related-node-text {
    @apply text-[16px] font-bold font-[Hind] text-[#2A3439] mb-4 p-3
}

.architecture-requirement-child-node-not-found {
    @apply flex items-center justify-center -mt-[20px]
}

/* architecture requirements child node css alignments ends */