@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Container Styles */
  .status-container {
    @apply sticky top-0 z-50 bg-white shadow-md;
  }

  .status-wrapper {
    @apply p-1.5 bg-white border border-gray-200;
  }

  .status-loading {
    @apply sticky top-0 z-50 bg-white;
  }

  /* Toggle Button */
  .status-toggle-button {
    @apply w-full flex items-center justify-between px-4 py-2 text-sm font-medium
           text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2
           focus:ring-orange-500 focus:ring-opacity-50 rounded-md transition-colors duration-200;
  }



  .status-chevron {
    @apply h-4 w-4 transition-transform duration-200;
  }

  /* Content Area */
  .status-content {
    @apply p-4 space-y-2;
  }

  /* Status Field */
  .status-field {
    @apply flex items-start;
  }

  .status-label {
    @apply font-semibold w-24 text-gray-600;
  }

  .status-description {
    @apply text-gray-800 flex-1;
  }

  /* Status Badge */
  .status-badge {
    @apply px-2 py-1 rounded-full text-sm font-medium;
  }

  .status-badge-default {
    @apply bg-gray-100 text-gray-800;
  }

  .status-badge-success {
    @apply bg-green-100 text-green-800;
  }

  .status-badge-warning {
    @apply bg-yellow-100 text-yellow-800;
  }

  .status-badge-info {
    @apply bg-orange-100 text-orange-800;
  }

  .status-badge-error {
    @apply bg-red-100 text-red-800;
  }

  .status-badge-purple {
    @apply bg-purple-100 text-purple-800;
  }

  /* Links */
  .status-link {
    @apply text-orange-600 hover:text-orange-800 underline
           transition-colors duration-200;
  }
}