@tailwind base;
@tailwind components;
@tailwind utilities;

@layer components {
  /* Layout Components */
  .browse-panel-container {
    @apply w-full h-full flex flex-col gap-4 p-4;
  }

  .browse-panel-empty {
    @apply text-center flex justify-center h-96 items-center;
  }

  /* Details Panel */
  .browse-panel-details {
    @apply mb-3 p-1.5 bg-white rounded-lg shadow border border-orange-200;
  }

  .browse-panel-content {
    @apply p-4 space-y-2;
  }

  /* Toggle Button */
  .browse-panel-toggle-button {
    @apply w-full flex items-center justify-between px-4 py-2 text-sm font-medium
           text-orange-700 hover:bg-orange-100 focus:outline-none transition-colors duration-200;
  }

  .browse-panel-heading {
    @apply font-medium;
  }

  .browse-panel-icon {
    @apply h-4 w-4 transition-transform duration-200;
  }

  /* Detail Row Components */
  .browse-panel-row {
    @apply flex items-start gap-4;
  }

  .browse-panel-label {
    @apply font-semibold w-24 text-custom-text-muted flex-shrink-0;
  }

  .browse-panel-value {
    @apply flex-1 break-words;
  }

  /* Value Variants */
  .browse-panel-value-default {
    @apply text-custom-text-primary;
  }

  .browse-panel-value-primary {
    @apply text-primary;
  }

  .browse-panel-value-success {
    @apply text-success;
  }

  .browse-panel-value-warning {
    @apply text-warning;
  }

  .browse-panel-value-muted {
    @apply text-custom-text-muted;
  }

  /* Output Section */
  .browse-panel-output {
    @apply w-full h-[77vh] flex items-center justify-center bg-custom-bg-muted
           rounded-lg overflow-hidden;
  }

  .browse-panel-image {
    @apply max-w-full max-h-full object-contain;
  }
}