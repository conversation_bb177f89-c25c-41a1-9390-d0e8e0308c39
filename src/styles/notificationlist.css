/* @import url("https://fonts.googleapis.com/css2?family=Hind:wght@300;400;500;600;700&display=swap"); */
/* Base container styles */

.notification-container {
  @apply flex flex-col h-full bg-custom-bg-notification-default;
  }
  
  .notification-scroll-area {
  @apply overflow-y-auto flex-grow;
  }
  
  /* Date header */
  
  .notification-date-header {
  @apply font-bold text-primary-500 capitalize;
  }
  
  /* Group container */
  
  .notification-group {
  @apply p-custom-4 pb-0 space-y-3;
  }
  
  /* Notification item */
  
  .notification-item {
  @apply p-custom-3 cursor-pointer rounded-custom-lg border border-custom-border-notification bg-white shadow-sm hover:shadow-md;
  }
  
  .notification-item-unread {
  @apply bg-custom-bg-notification-unread;
  }
  
  /* Notification content layout */
  
  .notification-content-wrapper {
  @apply flex items-start justify-between flex-grow;
  }
  
  /* Bell icon container */
  
  .notification-bell-container {
  @apply flex-shrink-0 relative mt-6 mr-custom-2 p-0;
  }
  
  .notification-bell-icon {
  @apply size-custom-5 transition-colors duration-200;
  }
  
  .notification-bell-icon-read {
  @apply text-custom-text-muted cursor-auto;
  }
  
  .notification-bell-icon-unread {
  @apply text-primary-500 group-hover:text-primary-600 group-hover:fill-current;
  }
  
  /* Unread indicator dot */
  
  .notification-unread-indicator {
  @apply absolute top-0 right-0 block size-custom-2 rounded-custom-full bg-destructive-500 ring-2 ring-white group-hover:bg-destructive-600;
  }
  
  /* Text content */
  
  .notification-content {
  @apply flex flex-col;
  }
  
  .notification-type {
  @apply font-bold text-custom-lg leading-custom-tight capitalize;
  }
  
  .notification-message {
  @apply text-custom-text-muted text-custom-base break-words overflow-hidden;
  }
  
  .notification-timestamp {
  @apply flex items-center justify-between
  }
  
  .notification-timestamp {
  @apply mr-custom-2 text-custom-xs text-custom-text-muted;
  }
  
  /* Delete button */
  
  .notification-delete-button {
  @apply hidden group-hover:inline-flex justify-center w-full text-custom-sm font-medium text-custom-text-muted hover:text-destructive-500 transition-colors duration-200;
  }
  
  .notification-delete-icon {
  @apply size-custom-5;
  }
  
  /* Footer */
  
  .notification-footer {
  @apply flex justify-center items-center py-custom-2 bg-white border-t border-custom-border hover:bg-custom-bg-notification-unread;
  }
  
  .mark-all-read-button {
  @apply font-bold text-primary-500 text-center cursor-pointer hover:underline;
  }
  
  /* Clear all button */
  
  .clear-all-button {
  @apply absolute bottom-16 right-custom-4 bg-destructive-500 hover:bg-destructive-600 text-white font-bold py-custom-3 px-custom-3 rounded-custom-full shadow-lg transition-colors duration-200 flex items-center space-x-custom-2 text-custom-sm;
  }
  
  .clear-all-icon {
  @apply size-custom-4 transition-transform duration-200 ease-in-out group-hover:rotate-90;
  }